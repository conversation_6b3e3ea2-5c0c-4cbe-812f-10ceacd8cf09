import { SUBJECTS } from '@/lib/constants';
import { QuestionContent } from '@/components/dashboard/question-content';

export function generateStaticParams() {
  return SUBJECTS.map((subject) => ({
    id: subject.id.toString(),
  }));
}

export default function QuestionPage({ params }: { params: { id: string } }) {
  const subjectId = Number(params.id);
  
  return <QuestionContent subjectId={subjectId} />;
}