/*
  # Add fields to user_progress table

  1. Changes
    - Add `user_level` column (integer, 1-10)
    - Add `hours` column (numeric)
    
  2. Security
    - Add check constraints for valid ranges
*/

ALTER TABLE user_progress
ADD COLUMN IF NOT EXISTS user_level numeric NOT NULL DEFAULT 1 CHECK (user_level BETWEEN 1 AND 10),
ADD COLUMN IF NOT EXISTS hours numeric NOT NULL DEFAULT 0 CHECK (hours >= 0);