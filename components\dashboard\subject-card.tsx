"use client";

import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, BookOpen } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';

interface Subject {
  id: number;
  name: string;
  institution_id: string;
  icon: string;
  description: string;
  color: string;
}

interface SubjectCardProps {
  subject: Subject;
}

export function SubjectCard({ subject }: SubjectCardProps) {
  // Generate a random progress value for demonstration
  const progress = Math.floor(Math.random() * 100);
  
  return (
    <motion.div 
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full overflow-hidden border-gray-200 dark:border-gray-800 hover:border-blue-200 dark:hover:border-blue-800 transition-colors">
        <CardContent className="p-6">
          <div className={`w-12 h-12 rounded-lg ${subject.color} flex items-center justify-center mb-4`}>
            <BookOpen className="h-6 w-6" />
          </div>
          <h3 className="font-bold text-lg mb-2 line-clamp-1">{subject.name}</h3>
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{subject.description}</p>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progreso</span>
              <span className="text-sm text-muted-foreground">{progress}%</span>
            </div>
            <Progress value={progress} className="h-1.5" />
          </div>
        </CardContent>
        <CardFooter className="bg-gray-50 dark:bg-gray-900 p-4">
          <Link href={`/questions/${subject.id}`} className="w-full">
            <Button variant="outline" className="w-full">
              <BookOpen className="h-4 w-4 mr-2" />
              Practicar
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </motion.div>
  );
}