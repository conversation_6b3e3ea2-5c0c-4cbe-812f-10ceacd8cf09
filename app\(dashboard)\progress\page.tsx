"use client";

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend } from 'recharts';
import { CalendarIcon, ChevronLeft, ChevronRight, Clock, Download, Star } from 'lucide-react';

const subjectPerformance = [
  { subject: "Matemática", correct: 85, incorrect: 15 },
  { subject: "Física", correct: 70, incorrect: 30 },
  { subject: "Química", correct: 60, incorrect: 40 },
  { subject: "Historia", correct: 75, incorrect: 25 },
  { subject: "Geometría", correct: 90, incorrect: 10 },
];

const timeSpent = [
  { day: "Lun", hours: 2.5 },
  { day: "Mar", hours: 1.8 },
  { day: "<PERSON><PERSON>", hours: 3.2 },
  { day: "<PERSON><PERSON>", hours: 2.0 },
  { day: "Vie", hours: 2.7 },
  { day: "S<PERSON>b", hours: 4.5 },
  { day: "Dom", hours: 3.0 },
];

const DIFFICULTY_COLORS = ["#10b981", "#fbbf24", "#f59e0b", "#f97316", "#ef4444"];

const difficultyData = [
  { name: "Nivel 1-2", value: 25, color: DIFFICULTY_COLORS[0] },
  { name: "Nivel 3-4", value: 40, color: DIFFICULTY_COLORS[1] },
  { name: "Nivel 5-6", value: 20, color: DIFFICULTY_COLORS[2] },
  { name: "Nivel 7-8", value: 10, color: DIFFICULTY_COLORS[3] },
  { name: "Nivel 9-10", value: 5, color: DIFFICULTY_COLORS[4] },
];

export default function ProgressPage() {
  const [dateRange, setDateRange] = useState("last-week");

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-1">
          Tu progreso
        </h2>
        <p className="text-muted-foreground">
          Visualiza y analiza tu rendimiento académico
        </p>
      </div>
      
      <div className="flex items-center justify-between">
        <Tabs defaultValue="last-week" value={dateRange} onValueChange={setDateRange}>
          <TabsList className="grid grid-cols-3 w-[300px]">
            <TabsTrigger value="last-week">Última semana</TabsTrigger>
            <TabsTrigger value="last-month">Último mes</TabsTrigger>
            <TabsTrigger value="all-time">Todo</TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" className="h-8 w-8">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            <CalendarIcon className="mr-2 h-4 w-4" />
            May 2025
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Preguntas respondidas
            </CardTitle>
            <div className="h-4 w-4 text-blue-600">
              <BookOpen className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">
              +14% desde la semana pasada
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Porcentaje de acierto
            </CardTitle>
            <div className="h-4 w-4 text-green-600">
              <CheckCircle className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">74%</div>
            <p className="text-xs text-muted-foreground">
              +5% desde la semana pasada
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Horas de estudio
            </CardTitle>
            <div className="h-4 w-4 text-amber-600">
              <Clock className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18h 25m</div>
            <p className="text-xs text-muted-foreground">
              +2.5h desde la semana pasada
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Nivel promedio
            </CardTitle>
            <div className="h-4 w-4 text-purple-600">
              <Star className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5.8</div>
            <p className="text-xs text-muted-foreground">
              +0.3 desde la semana pasada
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Rendimiento por materia</CardTitle>
            <CardDescription>
              Porcentaje de respuestas correctas e incorrectas
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={subjectPerformance}
                  layout="vertical"
                  margin={{ top: 20, right: 30, left: 40, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                  <XAxis type="number" domain={[0, 100]} />
                  <YAxis 
                    dataKey="subject" 
                    type="category" 
                    scale="band"
                    width={80}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip 
                    formatter={(value) => [`${value}%`, '']}
                    labelFormatter={(label) => `Materia: ${label}`}
                  />
                  <Bar dataKey="correct" stackId="a" fill="#22c55e" name="Correctas" />
                  <Bar dataKey="incorrect" stackId="a" fill="#ef4444" name="Incorrectas" />
                  <Legend />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Distribución por dificultad</CardTitle>
            <CardDescription>
              Preguntas respondidas según nivel de dificultad
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={difficultyData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    innerRadius={40}
                    paddingAngle={5}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {difficultyData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle>Tiempo de estudio</CardTitle>
            <CardDescription>
              Horas dedicadas al estudio por día
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" className="mt-4 sm:mt-0">
            <Download className="mr-2 h-4 w-4" />
            Descargar reporte
          </Button>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={timeSpent}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip 
                  formatter={(value) => [`${value} horas`, 'Tiempo de estudio']}
                />
                <Bar 
                  dataKey="hours" 
                  fill="#3b82f6" 
                  radius={[4, 4, 0, 0]}
                  name="Horas de estudio"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function BookOpen(props) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
    </svg>
  );
}

function CheckCircle(props) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
      <polyline points="22 4 12 14.01 9 11.01" />
    </svg>
  );
}