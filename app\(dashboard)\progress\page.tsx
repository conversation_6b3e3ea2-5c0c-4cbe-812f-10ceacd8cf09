"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend } from 'recharts';
import { CalendarIcon, ChevronLeft, ChevronRight, Clock, Download, Star, BookOpen, CheckCircle, Loader2 } from 'lucide-react';
import { getSession } from '@/lib/actions/auth';
import { getUserProgressStats } from '@/lib/actions/user';

const DIFFICULTY_COLORS = ["#10b981", "#fbbf24", "#f59e0b", "#f97316", "#ef4444"];

interface ProgressData {
  progress: any[];
  responses: any[];
}

export default function ProgressPage() {
  const [dateRange, setDateRange] = useState("last-week");
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchProgressData() {
      try {
        const session = await getSession();
        if (session?.user) {
          const data = await getUserProgressStats(session.user.id);
          setProgressData(data);
        }
      } catch (error) {
        console.error('Error fetching progress data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchProgressData();
  }, []);

  // Calculate statistics from real data
  const calculateStats = () => {
    if (!progressData) return null;

    const totalCorrect = progressData.progress.reduce((sum, p) => sum + p.correct_answers, 0);
    const totalIncorrect = progressData.progress.reduce((sum, p) => sum + p.incorrect_answers, 0);
    const totalAnswers = totalCorrect + totalIncorrect;
    const totalHours = progressData.progress.reduce((sum, p) => sum + p.hours, 0);
    const averageLevel = progressData.progress.length > 0
      ? progressData.progress.reduce((sum, p) => sum + p.user_level, 0) / progressData.progress.length
      : 0;

    return {
      totalAnswers,
      totalCorrect,
      totalIncorrect,
      accuracyPercentage: totalAnswers > 0 ? Math.round((totalCorrect / totalAnswers) * 100) : 0,
      totalHours: Math.round(totalHours * 100) / 100,
      averageLevel: Math.round(averageLevel * 10) / 10
    };
  };

  // Generate subject performance data
  const getSubjectPerformance = () => {
    if (!progressData) return [];

    return progressData.progress.map(p => ({
      subject: p.subjects?.name || 'Materia',
      correct: p.correct_answers,
      incorrect: p.incorrect_answers,
      correctPercentage: p.correct_answers + p.incorrect_answers > 0
        ? Math.round((p.correct_answers / (p.correct_answers + p.incorrect_answers)) * 100)
        : 0
    }));
  };

  // Generate difficulty distribution data
  const getDifficultyData = () => {
    if (!progressData) return [];

    const difficultyCount = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

    progressData.responses.forEach(response => {
      const difficulty = response.questions?.difficulty || 1;
      if (difficulty <= 2) difficultyCount[1]++;
      else if (difficulty <= 4) difficultyCount[2]++;
      else if (difficulty <= 6) difficultyCount[3]++;
      else if (difficulty <= 8) difficultyCount[4]++;
      else difficultyCount[5]++;
    });

    const total = Object.values(difficultyCount).reduce((sum, count) => sum + count, 0);

    if (total === 0) return [];

    return [
      { name: "Nivel 1-2", value: difficultyCount[1], color: DIFFICULTY_COLORS[0] },
      { name: "Nivel 3-4", value: difficultyCount[2], color: DIFFICULTY_COLORS[1] },
      { name: "Nivel 5-6", value: difficultyCount[3], color: DIFFICULTY_COLORS[2] },
      { name: "Nivel 7-8", value: difficultyCount[4], color: DIFFICULTY_COLORS[3] },
      { name: "Nivel 9-10", value: difficultyCount[5], color: DIFFICULTY_COLORS[4] },
    ].filter(item => item.value > 0);
  };

  const stats = calculateStats();
  const subjectPerformance = getSubjectPerformance();
  const difficultyData = getDifficultyData();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-1">
          Tu progreso
        </h2>
        <p className="text-muted-foreground">
          Visualiza y analiza tu rendimiento académico
        </p>
      </div>
      
      <div className="flex items-center justify-between">
        <Tabs defaultValue="last-week" value={dateRange} onValueChange={setDateRange}>
          <TabsList className="grid grid-cols-3 w-[300px]">
            <TabsTrigger value="last-week">Última semana</TabsTrigger>
            <TabsTrigger value="last-month">Último mes</TabsTrigger>
            <TabsTrigger value="all-time">Todo</TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" className="h-8 w-8">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" className="h-8">
            <CalendarIcon className="mr-2 h-4 w-4" />
            May 2025
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Preguntas respondidas
            </CardTitle>
            <div className="h-4 w-4 text-blue-600">
              <BookOpen className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalAnswers || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats?.totalCorrect || 0} correctas, {stats?.totalIncorrect || 0} incorrectas
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Porcentaje de acierto
            </CardTitle>
            <div className="h-4 w-4 text-green-600">
              <CheckCircle className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.accuracyPercentage || 0}%</div>
            <p className="text-xs text-muted-foreground">
              {stats?.totalAnswers ? 'Basado en ' + stats.totalAnswers + ' respuestas' : 'Sin datos aún'}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Horas de estudio
            </CardTitle>
            <div className="h-4 w-4 text-amber-600">
              <Clock className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalHours || 0}h</div>
            <p className="text-xs text-muted-foreground">
              Tiempo total acumulado
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Nivel promedio
            </CardTitle>
            <div className="h-4 w-4 text-purple-600">
              <Star className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.averageLevel || 0}</div>
            <p className="text-xs text-muted-foreground">
              Promedio en todas las materias
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Rendimiento por materia</CardTitle>
            <CardDescription>
              Respuestas correctas e incorrectas por materia
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[300px]">
              {subjectPerformance.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={subjectPerformance}
                    layout="vertical"
                    margin={{ top: 20, right: 30, left: 40, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                    <XAxis type="number" />
                    <YAxis
                      dataKey="subject"
                      type="category"
                      scale="band"
                      width={80}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip
                      formatter={(value, name) => [value, name === 'correct' ? 'Correctas' : 'Incorrectas']}
                      labelFormatter={(label) => `Materia: ${label}`}
                    />
                    <Bar dataKey="correct" stackId="a" fill="#22c55e" name="Correctas" />
                    <Bar dataKey="incorrect" stackId="a" fill="#ef4444" name="Incorrectas" />
                    <Legend />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No hay datos de progreso aún</p>
                    <p className="text-sm">Responde algunas preguntas para ver tu rendimiento</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Distribución por dificultad</CardTitle>
            <CardDescription>
              Preguntas respondidas según nivel de dificultad
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {difficultyData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={difficultyData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      innerRadius={40}
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                    >
                      {difficultyData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} preguntas`, 'Cantidad']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No hay datos de dificultad aún</p>
                    <p className="text-sm">Responde preguntas para ver la distribución</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle>Tiempo de estudio por materia</CardTitle>
            <CardDescription>
              Horas dedicadas al estudio en cada materia
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" className="mt-4 sm:mt-0">
            <Download className="mr-2 h-4 w-4" />
            Descargar reporte
          </Button>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            {subjectPerformance.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={progressData?.progress.map(p => ({
                    subject: p.subjects?.name || 'Materia',
                    hours: Math.round(p.hours * 100) / 100
                  })) || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="subject" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`${value} horas`, 'Tiempo de estudio']}
                  />
                  <Bar
                    dataKey="hours"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                    name="Horas de estudio"
                  />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No hay datos de tiempo aún</p>
                  <p className="text-sm">El tiempo se registra automáticamente al responder preguntas</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}