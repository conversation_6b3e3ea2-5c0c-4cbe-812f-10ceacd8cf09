"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend } from 'recharts';
import { CalendarIcon, ChevronLeft, ChevronRight, Clock, Download, Star, BookOpen, CheckCircle, Loader2 } from 'lucide-react';
import { getUserProgressStats } from '@/lib/queries/questions';
import { supabase } from '@/lib/supabase/client';

const DIFFICULTY_COLORS = ["#10b981", "#fbbf24", "#f59e0b", "#f97316", "#ef4444"];

interface ProgressData {
  progress: any[];
  responses: any[];
}

export default function ProgressPage() {
  const [dateRange, setDateRange] = useState("last-week");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Format month for display
  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      month: 'long',
      year: 'numeric'
    });
  };

  // Format month for API (YYYY-MM)
  const formatMonthForAPI = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  };

  // Navigate months
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  useEffect(() => {
    async function fetchProgressData() {
      setIsLoading(true);
      try {
        // Get current user session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError || !session?.user) {
          console.error('Session error:', sessionError);
          setProgressData({ progress: [], responses: [] });
          return;
        }

        // Determine which filter to use
        let data;
        if (dateRange === 'specific-month') {
          // Use specific month filter
          data = await getUserProgressStats(session.user.id, undefined, formatMonthForAPI(currentMonth));
        } else {
          // Use date range filter
          data = await getUserProgressStats(session.user.id, dateRange);
        }

        setProgressData(data);
      } catch (error) {
        console.error('Error fetching progress data:', error);
        setProgressData({ progress: [], responses: [] });
      } finally {
        setIsLoading(false);
      }
    }

    fetchProgressData();
  }, [dateRange, currentMonth]);

  // Get period description for display
  const getPeriodDescription = () => {
    switch (dateRange) {
      case 'last-week':
        return 'en la última semana';
      case 'last-month':
        return 'en el último mes';
      case 'specific-month':
        return `en ${formatMonth(currentMonth).toLowerCase()}`;
      case 'all-time':
      default:
        return 'en total';
    }
  };

  // Calculate statistics from real data
  const calculateStats = () => {
    if (!progressData) return null;

    const totalCorrect = progressData.progress.reduce((sum, p) => sum + (p.correct_answers || 0), 0);
    const totalIncorrect = progressData.progress.reduce((sum, p) => sum + (p.incorrect_answers || 0), 0);
    const totalAnswers = totalCorrect + totalIncorrect;

    // Calculate estimated hours based on responses (assuming 1 minute per question)
    const totalHours = Math.round((progressData.responses.length / 60) * 100) / 100;

    // Calculate average accuracy as level
    const averageAccuracy = totalAnswers > 0 ? (totalCorrect / totalAnswers) * 10 : 0;

    return {
      totalAnswers,
      totalCorrect,
      totalIncorrect,
      accuracyPercentage: totalAnswers > 0 ? Math.round((totalCorrect / totalAnswers) * 100) : 0,
      totalHours,
      averageLevel: Math.round(averageAccuracy * 10) / 10
    };
  };

  // Generate subject performance data
  const getSubjectPerformance = () => {
    if (!progressData) return [];

    return progressData.progress.map(p => ({
      subject: p.subjects?.name || 'Materia',
      correct: p.correct_answers || 0,
      incorrect: p.incorrect_answers || 0,
      correctPercentage: (p.correct_answers || 0) + (p.incorrect_answers || 0) > 0
        ? Math.round(((p.correct_answers || 0) / ((p.correct_answers || 0) + (p.incorrect_answers || 0))) * 100)
        : 0
    }));
  };

  // Generate difficulty distribution data
  const getDifficultyData = () => {
    if (!progressData) return [];

    const difficultyCount = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };

    progressData.responses.forEach(response => {
      const difficulty = response.questions?.difficulty || 1;
      if (difficulty <= 2) difficultyCount[1]++;
      else if (difficulty <= 4) difficultyCount[2]++;
      else if (difficulty <= 6) difficultyCount[3]++;
      else if (difficulty <= 8) difficultyCount[4]++;
      else difficultyCount[5]++;
    });

    const total = Object.values(difficultyCount).reduce((sum, count) => sum + count, 0);

    if (total === 0) return [];

    return [
      { name: "Nivel 1-2", value: difficultyCount[1], color: DIFFICULTY_COLORS[0] },
      { name: "Nivel 3-4", value: difficultyCount[2], color: DIFFICULTY_COLORS[1] },
      { name: "Nivel 5-6", value: difficultyCount[3], color: DIFFICULTY_COLORS[2] },
      { name: "Nivel 7-8", value: difficultyCount[4], color: DIFFICULTY_COLORS[3] },
      { name: "Nivel 9-10", value: difficultyCount[5], color: DIFFICULTY_COLORS[4] },
    ].filter(item => item.value > 0);
  };

  const stats = calculateStats();
  const subjectPerformance = getSubjectPerformance();
  const difficultyData = getDifficultyData();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-1">
          Tu progreso {getPeriodDescription()}
        </h2>
        <p className="text-muted-foreground">
          Visualiza y analiza tu rendimiento académico
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <Tabs defaultValue="last-week" value={dateRange} onValueChange={setDateRange}>
          <TabsList className="grid grid-cols-4 w-[400px]">
            <TabsTrigger value="last-week">Última semana</TabsTrigger>
            <TabsTrigger value="last-month">Último mes</TabsTrigger>
            <TabsTrigger value="all-time">Todo</TabsTrigger>
            <TabsTrigger value="specific-month">Por mes</TabsTrigger>
          </TabsList>
        </Tabs>

        {dateRange === 'specific-month' && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => navigateMonth('prev')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" className="h-8 min-w-[140px]">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {formatMonth(currentMonth)}
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => navigateMonth('next')}
              disabled={currentMonth >= new Date()}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Preguntas respondidas
            </CardTitle>
            <div className="h-4 w-4 text-blue-600">
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <BookOpen className="h-4 w-4" />}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoading ? '...' : (stats?.totalAnswers || 0)}</div>
            <p className="text-xs text-muted-foreground">
              {isLoading ? 'Cargando...' : `${stats?.totalCorrect || 0} correctas, ${stats?.totalIncorrect || 0} incorrectas`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Porcentaje de acierto
            </CardTitle>
            <div className="h-4 w-4 text-green-600">
              <CheckCircle className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.accuracyPercentage || 0}%</div>
            <p className="text-xs text-muted-foreground">
              {stats?.totalAnswers ? `Basado en ${stats.totalAnswers} respuestas ${getPeriodDescription()}` : `Sin datos ${getPeriodDescription()}`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Horas de estudio
            </CardTitle>
            <div className="h-4 w-4 text-amber-600">
              <Clock className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalHours || 0}h</div>
            <p className="text-xs text-muted-foreground">
              Tiempo estimado {getPeriodDescription()}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Nivel promedio
            </CardTitle>
            <div className="h-4 w-4 text-purple-600">
              <Star className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.averageLevel || 0}</div>
            <p className="text-xs text-muted-foreground">
              Promedio {getPeriodDescription()}
            </p>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Rendimiento por materia</CardTitle>
            <CardDescription>
              Respuestas correctas e incorrectas por materia
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[300px]">
              {subjectPerformance.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={subjectPerformance}
                    layout="vertical"
                    margin={{ top: 20, right: 30, left: 40, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                    <XAxis type="number" />
                    <YAxis
                      dataKey="subject"
                      type="category"
                      scale="band"
                      width={80}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip
                      formatter={(value, name) => [value, name === 'correct' ? 'Correctas' : 'Incorrectas']}
                      labelFormatter={(label) => `Materia: ${label}`}
                    />
                    <Bar dataKey="correct" stackId="a" fill="#22c55e" name="Correctas" />
                    <Bar dataKey="incorrect" stackId="a" fill="#ef4444" name="Incorrectas" />
                    <Legend />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No hay datos de progreso {getPeriodDescription()}</p>
                    <p className="text-sm">Responde algunas preguntas para ver tu rendimiento</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Distribución por dificultad</CardTitle>
            <CardDescription>
              Preguntas respondidas según nivel de dificultad
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {difficultyData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={difficultyData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      innerRadius={40}
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                    >
                      {difficultyData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} preguntas`, 'Cantidad']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No hay datos de dificultad {getPeriodDescription()}</p>
                    <p className="text-sm">Responde preguntas para ver la distribución</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle>Tiempo de estudio por materia</CardTitle>
            <CardDescription>
              Horas dedicadas al estudio en cada materia
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" className="mt-4 sm:mt-0">
            <Download className="mr-2 h-4 w-4" />
            Descargar reporte
          </Button>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            {subjectPerformance.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={progressData?.progress.map(p => {
                    // Calculate estimated hours based on responses for this subject
                    const subjectResponses = progressData.responses.filter(r =>
                      r.questions?.subject_id === p.subject_id
                    );
                    const estimatedHours = Math.round((subjectResponses.length / 60) * 100) / 100;

                    return {
                      subject: p.subjects?.name || 'Materia',
                      hours: estimatedHours
                    };
                  }) || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="subject" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [`${value} horas`, 'Tiempo estimado']}
                  />
                  <Bar
                    dataKey="hours"
                    fill="#3b82f6"
                    radius={[4, 4, 0, 0]}
                    name="Horas estimadas"
                  />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No hay datos de tiempo {getPeriodDescription()}</p>
                  <p className="text-sm">El tiempo se estima basado en las preguntas respondidas</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}