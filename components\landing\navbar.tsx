"use client";

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ModeToggle } from '@/components/ui/mode-toggle';
import { GraduationCap, Menu, X } from 'lucide-react';
import { APP_NAME } from '@/lib/constants';
import { cn } from '@/lib/utils';

export function LandingNavbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <header 
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-300", 
        isScrolled 
          ? "bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-sm py-3" 
          : "bg-transparent py-5"
      )}
    >
      <div className="container flex justify-between items-center">
        <Link href="/" className="flex items-center gap-2 font-bold text-xl">
          <GraduationCap className="h-6 w-6" />
          <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            {APP_NAME}
          </span>
        </Link>
        
        <nav className="hidden md:flex items-center gap-6">
          <Link 
            href="/#features" 
            className="font-medium text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors"
          >
            Características
          </Link>
          <Link 
            href="/#institutions" 
            className="font-medium text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors"
          >
            Universidades
          </Link>
          <Link 
            href="/#pricing" 
            className="font-medium text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors"
          >
            Precios
          </Link>
        </nav>
        
        <div className="hidden md:flex items-center gap-4">
          <ModeToggle />
          <Link href="/login">
            <Button variant="outline" size="sm">Iniciar Sesión</Button>
          </Link>
          <Link href="/register">
            <Button size="sm">Registrarse</Button>
          </Link>
        </div>

        <div className="flex items-center gap-2 md:hidden">
          <ModeToggle />
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-9 w-9" 
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X /> : <Menu />}
          </Button>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden fixed inset-0 top-16 bg-white dark:bg-gray-900 z-50 p-4">
          <nav className="flex flex-col gap-4 p-4">
            <Link 
              href="/#features" 
              className="py-2 px-4 text-lg font-medium border-b border-gray-200 dark:border-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              Características
            </Link>
            <Link 
              href="/#institutions" 
              className="py-2 px-4 text-lg font-medium border-b border-gray-200 dark:border-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              Universidades
            </Link>
            <Link 
              href="/#pricing" 
              className="py-2 px-4 text-lg font-medium border-b border-gray-200 dark:border-gray-700"
              onClick={() => setMobileMenuOpen(false)}
            >
              Precios
            </Link>
            <div className="flex flex-col gap-3 mt-4">
              <Link href="/login" onClick={() => setMobileMenuOpen(false)}>
                <Button variant="outline" className="w-full">Iniciar Sesión</Button>
              </Link>
              <Link href="/register" onClick={() => setMobileMenuOpen(false)}>
                <Button className="w-full">Registrarse</Button>
              </Link>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}