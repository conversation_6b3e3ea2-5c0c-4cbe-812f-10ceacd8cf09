"use client";

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { ModeToggle } from '@/components/ui/mode-toggle';
import { Button } from '@/components/ui/button';
import { Bell, Search } from 'lucide-react';
import { getSession } from '@/lib/actions/auth';
import { getUserSubscription } from '@/lib/actions/user';

interface UserData {
  subscription_status: string;
}

export function DashboardHeader() {
  const pathname = usePathname();
  const [userData, setUserData] = useState<UserData | null>(null);
  
  // Get the page title from the pathname
  const getPageTitle = () => {
    switch (pathname) {
      case '/dashboard':
        return 'Dashboard';
      case '/subjects':
        return 'Materias';
      case '/progress':
        return 'Progreso';
      case '/subscription':
        return 'Suscripción';
      case '/profile':
        return 'Perfil';
      default:
        if (pathname?.startsWith('/subjects/')) {
          return 'Materias';
        } else if (pathname?.startsWith('/questions/')) {
          return 'Pregunta';
        }
        return 'Dashboard';
    }
  };
  
  useEffect(() => {
    async function getUserData() {
      try {
        const session = await getSession();

        if (session?.user) {
          const data = await getUserSubscription(session.user.id);
          if (data) {
            setUserData(data);
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    }

    getUserData();
  }, []);

  return (
    <header className="sticky top-0 z-30 w-full border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
      <div className="px-4 md:px-6 h-16 flex items-center justify-between">
        <h1 className="text-xl font-bold">{getPageTitle()}</h1>
        
        <div className="flex items-center space-x-4">
          {userData?.subscription_status !== 'active' && (
            <Button size="sm\" variant="default\" className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
              Actualiza tu plan
            </Button>
          )}
          
          <div className="hidden md:flex items-center rounded-md border border-gray-200 dark:border-gray-700 px-3 h-9">
            <Search className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <input 
              className="flex-1 ml-2 bg-transparent outline-none text-sm placeholder:text-gray-500 dark:placeholder:text-gray-400" 
              placeholder="Buscar..."
            />
          </div>
          
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
            <span className="sr-only">Notificaciones</span>
          </Button>
          
          <ModeToggle />
        </div>
      </div>
    </header>
  );
}