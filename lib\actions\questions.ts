"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { updateUserProgress } from "./user";

// NOTE: getQuestionCount and getFirstQuestion moved to /lib/queries/questions.ts for direct client access

export async function submitAnswer(questionId: number, userId: string, response: string, wasCorrect: boolean, subjectId: number, timeSpentSeconds: number) {
  const supabase = createServerActionClient({ cookies });

  // Insert the response
  const { error } = await supabase
    .from('responses')
    .insert({
      user_id: userId,
      question_id: questionId,
      response: parseInt(response), // Convert string to integer
      was_correct: wasCorrect
    });

  if (error) throw error;

  // Update user progress
  await updateUserProgress(userId, subjectId, wasCorrect, timeSpentSeconds);

  return true;
}

// NOTE: getUserProgressData moved to /lib/queries/questions.ts for direct client access

// NOTE: getUserProgressStats and processResponse<PERSON><PERSON><PERSON><PERSON><PERSON> moved to /lib/queries/questions.ts for direct client access