"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function getQuestionCount(subjectId: number) {
  const supabase = createServerActionClient({ cookies });
  const { count } = await supabase
    .from('questions')
    .select('*', { count: 'exact', head: true })
    .eq('subject_id', subjectId);
    
  return count || 0;
}

export async function getFirstQuestion(subjectId: number) {
  const supabase = createServerActionClient({ cookies });
  const { data: questions, error } = await supabase
    .from('questions')
    .select('*')
    .eq('subject_id', subjectId)
    .order('id', { ascending: true })
    .limit(1);

  if (error) throw error;
  
  if (questions && questions.length > 0) {
    const question = questions[0];
    // Format options
    question.options = question.options.map((opt: any, index: number) => ({
      id: index.toString(),
      text: opt.value,
      correct: opt.correct
    }));
    return question;
  }
  
  return null;
}

export async function submitAnswer(questionId: number, userId: string, response: string, wasCorrect: boolean) {
  const supabase = createServerActionClient({ cookies });
  const { error } = await supabase
    .from('responses')
    .insert({
      user_id: userId,
      question_id: questionId,
      response,
      was_correct: wasCorrect
    });

  if (error) throw error;
  
  return true;
}