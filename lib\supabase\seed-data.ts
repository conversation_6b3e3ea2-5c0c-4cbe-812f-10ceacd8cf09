import { supabase } from './client';

export async function seedDatabase() {
  try {
    // Check if data already exists
    const { count: institutionCount } = await supabase
      .from('institution')
      .select('*', { count: 'exact', head: true });
    
    if (institutionCount && institutionCount > 0) {
      console.log('Database already seeded');
      return;
    }

    // Insert institutions
    const { data: institutions, error: institutionError } = await supabase
      .from('institution')
      .insert([
        { name: 'Universidad Nacional Mayor de San Marcos' },
        { name: 'Universidad Nacional de Ingeniería' },
        { name: 'Pontificia Universidad Católica del Perú' },
        { name: 'Universidad Nacional Agraria La Molina' }
      ])
      .select();

    if (institutionError) throw institutionError;

    // Insert subjects
    const { error: subjectsError } = await supabase
      .from('subjects')
      .insert([
        { name: 'Matemática', institution_id: 1 },
        { name: 'Física', institution_id: 1 },
        { name: 'Química', institution_id: 1 },
        { name: '<PERSON>', institution_id: 1 },
        { name: 'Geometr<PERSON>', institution_id: 2 },
        { name: 'Literatura', institution_id: 3 }
      ]);

    if (subjectsError) throw subjectsError;
    
    // Insert questions (sample data)
    const { error: questionsError } = await supabase
      .from('questions')
      .insert([
        {
          question: '<p>Si f(x) = 3x² - 5x + 2, ¿cuál es el valor de f(2)?</p>',
          options: [
            { id: 'a', text: '4' },
            { id: 'b', text: '8' },
            { id: 'c', text: '10' },
            { id: 'd', text: '12' },
            { id: 'e', text: '16' }
          ],
          solution: '<p>Para calcular f(2), reemplazamos x = 2 en la función:</p><p>f(2) = 3(2)² - 5(2) + 2</p><p>f(2) = 3(4) - 10 + 2</p><p>f(2) = 12 - 10 + 2</p><p>f(2) = 8</p>',
          subject_id: 1,
          difficulty: 3
        },
        {
          question: '<p>En un triángulo rectángulo, si un cateto mide 6 cm y la hipotenusa mide 10 cm, ¿cuál es la longitud del otro cateto?</p>',
          options: [
            { id: 'a', text: '4 cm' },
            { id: 'b', text: '8 cm' },
            { id: 'c', text: '6 cm' },
            { id: 'd', text: '12 cm' },
            { id: 'e', text: '√64 cm' }
          ],
          solution: '<p>Aplicando el Teorema de Pitágoras:</p><p>a² + b² = c²</p><p>Sabemos que a = 6 cm y c = 10 cm</p><p>6² + b² = 10²</p><p>36 + b² = 100</p><p>b² = 64</p><p>b = 8 cm</p>',
          subject_id: 5,
          difficulty: 4
        },
        {
          question: '<p>¿Cuál es la fuerza resultante que actúa sobre un cuerpo de 2 kg que se mueve con una aceleración de 5 m/s²?</p>',
          options: [
            { id: 'a', text: '2.5 N' },
            { id: 'b', text: '7 N' },
            { id: 'c', text: '10 N' },
            { id: 'd', text: '0.4 N' },
            { id: 'e', text: '25 N' }
          ],
          solution: '<p>Aplicando la Segunda Ley de Newton:</p><p>F = m × a</p><p>F = 2 kg × 5 m/s²</p><p>F = 10 N</p>',
          subject_id: 2,
          difficulty: 2
        },
        {
          question: '<p>¿Cuál es la configuración electrónica del átomo de sodio (Na)?</p>',
          options: [
            { id: 'a', text: '1s² 2s² 2p⁶' },
            { id: 'b', text: '1s² 2s² 2p⁶ 3s¹' },
            { id: 'c', text: '1s² 2s² 2p⁶ 3s² 3p¹' },
            { id: 'd', text: '1s² 2s² 2p⁵' },
            { id: 'e', text: '1s² 2s² 2p⁶ 3s² 3p⁶' }
          ],
          solution: '<p>El sodio (Na) tiene el número atómico Z = 11, por lo que tiene 11 electrones.</p><p>La configuración electrónica es:</p><p>1s² 2s² 2p⁶ 3s¹</p>',
          subject_id: 3,
          difficulty: 5
        }
      ]);

    if (questionsError) throw questionsError;

    console.log('Database seeded successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}