export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      institutions: {
        Row: {
          id: string
          name: string
        }
        Insert: {
          id?: string
          name: string
        }
        Update: {
          id?: string
          name?: string
        }
        Relationships: []
      }
      questions: {
        Row: {
          id: number
          question: string
          options: Json
          solution: string
          image: string | null
          subject_id: number
          difficulty: number
          created_at: string
        }
        Insert: {
          id?: number
          question: string
          options: Json
          solution: string
          image?: string | null
          subject_id: number
          difficulty: number
          created_at?: string
        }
        Update: {
          id?: number
          question?: string
          options?: Json
          solution?: string
          image?: string | null
          subject_id?: number
          difficulty?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "questions_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          }
        ]
      }
      responses: {
        Row: {
          id: number
          user_id: string
          question_id: number
          response: string
          was_correct: boolean
          created_at: string
        }
        Insert: {
          id?: number
          user_id: string
          question_id: number
          response: string
          was_correct: boolean
          created_at?: string
        }
        Update: {
          id?: number
          user_id?: string
          question_id?: number
          response?: string
          was_correct?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "responses_question_id_fkey"
            columns: ["question_id"]
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "responses_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      subjects: {
        Row: {
          id: number
          name: string
          institution_id: string
        }
        Insert: {
          id?: number
          name: string
          institution_id: string
        }
        Update: {
          id?: number
          name?: string
          institution_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subjects_institution_id_fkey"
            columns: ["institution_id"]
            referencedRelation: "institution"
            referencedColumns: ["id"]
          }
        ]
      }
      user_progress: {
        Row: {
          id: number
          user_id: string
          subject_id: number
          correct_answers: number
          incorrect_answers: number
          last_updated: string
        }
        Insert: {
          id?: number
          user_id: string
          subject_id: number
          correct_answers?: number
          incorrect_answers?: number
          last_updated?: string
        }
        Update: {
          id?: number
          user_id?: string
          subject_id?: number
          correct_answers?: number
          incorrect_answers?: number
          last_updated?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_progress_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_progress_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          name: string
          email: string
          subscription_status: string
          subscription_expires: string | null
          created_at: string
        }
        Insert: {
          id: string
          name: string
          email: string
          subscription_status?: string
          subscription_expires?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          subscription_status?: string
          subscription_expires?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}