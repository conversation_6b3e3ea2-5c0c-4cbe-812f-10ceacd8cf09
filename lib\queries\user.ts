// Client-side queries for reading user data
// DIRECT SUPABASE CALLS - All data comes from client
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

const supabase = createClientComponentClient();

// ============================================
// CLIENT QUERIES - SOLO OPERACIONES DE LECTURA
// ============================================

export async function getSession() {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) throw error;
  return session;
}

export async function getUserProfile(userId: string) {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) throw error;
    if (!session?.user || session.user.id !== userId) throw new Error('User not found');

    return {
      id: session.user.id,
      email: session.user.email,
      name: session.user.user_metadata?.full_name || 'Usuario',
      subscription_status: session.user.user_metadata?.subscription_status || 'inactive',
      subscription_expires: session.user.user_metadata?.subscription_expires || null,
      created_at: session.user.created_at
    };
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
}

export async function getUserSubscription(userId: string) {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) throw error;
    if (!session?.user || session.user.id !== userId) throw new Error('User not found');

    return {
      subscription_status: session.user.user_metadata?.subscription_status || 'inactive',
      subscription_expires: session.user.user_metadata?.subscription_expires || null
    };
  } catch (error) {
    console.error('Error getting user subscription:', error);
    throw error;
  }
}

export async function getUserProgress(userId: string) {
  try {
    const { data, error } = await supabase
      .from('user_progress')
      .select('subject_id, correct_answers, incorrect_answers, user_level, hours')
      .eq('user_id', userId);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting user progress:', error);
    return [];
  }
}

export async function getRecentResponses(userId: string) {
  try {
    const { data, error } = await supabase
      .from('responses')
      .select(`
        id,
        question_id,
        response,
        was_correct,
        created_at
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting recent responses:', error);
    return [];
  }
}

export async function getUserDashboardData(userId?: string) {
  try {
    // Get user data from session
    const session = await getSession();

    if (!session?.user) {
      throw new Error('No session found');
    }

    const actualUserId = userId || session.user.id;

    const userData = {
      name: session.user.user_metadata?.full_name || 'Usuario',
      subscription_status: session.user.user_metadata?.subscription_status || 'inactive'
    };

    // Get progress with subject and institution data
    const { data: progress, error: progressError } = await supabase
      .from('user_progress')
      .select(`
        subject_id,
        correct_answers,
        incorrect_answers,
        user_level,
        hours,
        last_updated,
        subjects (
          name,
          institution_id,
          institutions (
            name
          )
        )
      `)
      .eq('user_id', actualUserId)
      .order('last_updated', { ascending: false });

    if (progressError) {
      console.error('Progress error:', progressError);
    }

    // Get recent responses with subject and institution data
    const { data: responses, error: responsesError } = await supabase
      .from('responses')
      .select(`
        id,
        question_id,
        response,
        was_correct,
        created_at,
        questions (
          subject_id,
          subjects (
            name,
            institution_id,
            institutions (
              name
            )
          )
        )
      `)
      .eq('user_id', actualUserId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (responsesError) {
      console.error('Responses error:', responsesError);
    }

    return {
      user: userData,
      progress: progress || [],
      responses: responses || []
    };
  } catch (error) {
    console.error('Error getting user dashboard data:', error);
    // Return basic user data even if other queries fail
    const session = await getSession();
    return {
      user: {
        name: session?.user?.user_metadata?.full_name || 'Usuario',
        subscription_status: session?.user?.user_metadata?.subscription_status || 'inactive'
      },
      progress: [],
      responses: []
    };
  }
}
