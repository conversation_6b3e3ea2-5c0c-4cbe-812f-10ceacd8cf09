// Client-side queries for reading user data
// NO DIRECT SUPABASE CALLS - All data comes from server actions (except auth session)
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import {
  getUserProfile as getUserProfileAction,
  getUserSubscription as getUserSubscriptionAction,
  getUserProgress as getUserProgressAction,
  getRecentResponses as getRecentResponsesAction,
  getUserDashboardData as getUserDashboardDataAction
} from '@/lib/actions/user';

const supabase = createClientComponentClient();

// ============================================
// CLIENT QUERIES - SOLO OPERACIONES DE LECTURA
// ============================================

// Only auth session is allowed to be called directly from client
export async function getSession() {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) throw error;
  return session;
}

export async function getUserProfile(userId: string) {
  try {
    return await getUserProfileAction(userId);
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
}

export async function getUserSubscription(userId: string) {
  try {
    return await getUserSubscriptionAction(userId);
  } catch (error) {
    console.error('Error getting user subscription:', error);
    throw error;
  }
}

export async function getUserProgress(userId: string) {
  try {
    return await getUserProgressAction(userId);
  } catch (error) {
    console.error('Error getting user progress:', error);
    return [];
  }
}

export async function getRecentResponses(userId: string) {
  try {
    return await getRecentResponsesAction(userId);
  } catch (error) {
    console.error('Error getting recent responses:', error);
    return [];
  }
}

export async function getUserDashboardData(userId?: string) {
  try {
    // Get session to determine userId if not provided
    const session = await getSession();
    const actualUserId = userId || session?.user?.id;

    if (!actualUserId) {
      throw new Error('No user ID available');
    }

    return await getUserDashboardDataAction(actualUserId);
  } catch (error) {
    console.error('Error getting user dashboard data:', error);
    // Return basic user data even if other queries fail
    const session = await getSession();
    return {
      user: {
        name: session?.user?.user_metadata?.full_name || 'Usuario',
        subscription_status: session?.user?.user_metadata?.subscription_status || 'inactive'
      },
      progress: [],
      responses: []
    };
  }
}
