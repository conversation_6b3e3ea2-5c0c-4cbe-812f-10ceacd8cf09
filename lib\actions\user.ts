"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function getUserProfile(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error) throw error;
  if (!user || user.id !== userId) throw new Error('User not found');

  return {
    id: user.id,
    email: user.email,
    name: user.user_metadata?.full_name || '',
    subscription_status: user.user_metadata?.subscription_status || 'inactive',
    subscription_expires: user.user_metadata?.subscription_expires || null,
    created_at: user.created_at
  };
}

export async function updateUserProfile(userId: string, data: any) {
  const supabase = createServerActionClient({ cookies });

  // Prepare update object
  const updateData: any = {};

  // If email is being updated, add it to the main update
  if (data.email) {
    updateData.email = data.email;
  }

  // Add metadata updates
  updateData.data = {};
  if (data.full_name) {
    updateData.data.full_name = data.full_name;
  }

  // Update user
  const { error } = await supabase.auth.updateUser(updateData);

  if (error) throw error;
  return true;
}

export async function getUserProgress(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('user_progress')
    .select(`
      *,
      subjects (
        name
      )
    `)
    .eq('user_id', userId);
    
  if (error) throw error;
  return data;
}

export async function getRecentResponses(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('responses')
    .select(`
      id,
      question_id,
      response,
      was_correct,
      created_at,
      questions (
        subject_id,
        subjects (
          name
        )
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (error) throw error;
  return data;
}

export async function getUserSubscription(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error) throw error;
  if (!user || user.id !== userId) throw new Error('User not found');

  return {
    subscription_status: user.user_metadata?.subscription_status || 'inactive',
    subscription_expires: user.user_metadata?.subscription_expires || null
  };
}

export async function updateUserSubscription(userId: string, subscriptionData: any) {
  const supabase = createServerActionClient({ cookies });

  // Update user metadata with subscription info
  const { error } = await supabase.auth.updateUser({
    data: {
      ...subscriptionData
    }
  });

  if (error) throw error;
  return true;
}

export async function getUserDashboardData(userId: string) {
  const supabase = createServerActionClient({ cookies });

  // Fetch user data from auth
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError) throw userError;
  if (!user || user.id !== userId) throw new Error('User not found');

  const userData = {
    name: user.user_metadata?.full_name || '',
    subscription_status: user.user_metadata?.subscription_status || 'inactive'
  };

  // Fetch user progress
  const { data: progress, error: progressError } = await supabase
    .from('user_progress')
    .select('subject_id, correct_answers, incorrect_answers, user_level, hours')
    .eq('user_id', userId);

  if (progressError) throw progressError;

  // Fetch recent responses with subject info
  const { data: responses, error: responsesError } = await supabase
    .from('responses')
    .select(`
      id,
      question_id,
      response,
      was_correct,
      created_at,
      questions!inner (
        subject_id,
        subjects!inner (
          name
        )
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (responsesError) throw responsesError;

  return {
    user: userData,
    progress,
    responses
  };
}

export async function updateUserProgress(userId: string, subjectId: number, wasCorrect: boolean, timeSpentSeconds: number) {
  const supabase = createServerActionClient({ cookies });

  // First, try to get existing progress
  const { data: existingProgress, error: selectError } = await supabase
    .from('user_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('subject_id', subjectId)
    .single();

  if (selectError && selectError.code !== 'PGRST116') { // PGRST116 is "not found" error
    throw selectError;
  }

  const timeSpentHours = timeSpentSeconds / 3600; // Convert seconds to hours

  if (existingProgress) {
    // Update existing record
    const updateData = {
      correct_answers: wasCorrect
        ? existingProgress.correct_answers + 1
        : existingProgress.correct_answers,
      incorrect_answers: wasCorrect
        ? existingProgress.incorrect_answers
        : existingProgress.incorrect_answers + 1,
      hours: existingProgress.hours + timeSpentHours,
      last_updated: new Date().toISOString()
    };

    const { error: updateError } = await supabase
      .from('user_progress')
      .update(updateData)
      .eq('user_id', userId)
      .eq('subject_id', subjectId);

    if (updateError) throw updateError;
  } else {
    // Insert new record
    const insertData = {
      user_id: userId,
      subject_id: subjectId,
      correct_answers: wasCorrect ? 1 : 0,
      incorrect_answers: wasCorrect ? 0 : 1,
      user_level: 5, // Default level as requested
      hours: timeSpentHours,
      last_updated: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('user_progress')
      .insert(insertData);

    if (insertError) throw insertError;
  }

  return true;
}

export async function getUserProgressStats(userId: string) {
  const supabase = createServerActionClient({ cookies });

  // Get user progress with subject names
  const { data: progressData, error: progressError } = await supabase
    .from('user_progress')
    .select(`
      *,
      subjects (
        name
      )
    `)
    .eq('user_id', userId);

  if (progressError) throw progressError;

  // Get responses with question difficulty
  const { data: responsesData, error: responsesError } = await supabase
    .from('responses')
    .select(`
      *,
      questions (
        difficulty,
        subject_id,
        subjects (
          name
        )
      )
    `)
    .eq('user_id', userId);

  if (responsesError) throw responsesError;

  return {
    progress: progressData || [],
    responses: responsesData || []
  };
}