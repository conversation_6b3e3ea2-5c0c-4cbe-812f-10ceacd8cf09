"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function getUserProfile(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
    
  if (error) throw error;
  return data;
}

export async function updateUserProfile(userId: string, data: any) {
  const supabase = createServerActionClient({ cookies });
  const { error } = await supabase
    .from('users')
    .update(data)
    .eq('id', userId);
    
  if (error) throw error;
  return true;
}

export async function getUserProgress(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('user_progress')
    .select(`
      *,
      subjects (
        name
      )
    `)
    .eq('user_id', userId);
    
  if (error) throw error;
  return data;
}

export async function getRecentResponses(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('responses')
    .select(`
      id,
      question_id,
      response,
      was_correct,
      created_at,
      questions (
        subject_id,
        subjects (
          name
        )
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(5);
    
  if (error) throw error;
  return data;
}