"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function getUserProfile(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    console.error('Session error in getUserProfile:', error);
    throw error;
  }

  if (!session?.user || session.user.id !== userId) {
    console.error('No session or user mismatch in getUserProfile:', { sessionUserId: session?.user?.id, requestedUserId: userId });
    throw new Error('User not found');
  }

  console.log('User profile data:', session.user.user_metadata);

  return {
    id: session.user.id,
    email: session.user.email,
    name: session.user.user_metadata?.full_name || 'Usuario',
    subscription_status: session.user.user_metadata?.subscription_status || 'inactive',
    subscription_expires: session.user.user_metadata?.subscription_expires || null,
    created_at: session.user.created_at
  };
}

export async function updateUserProfile(userId: string, data: any) {
  const supabase = createServerActionClient({ cookies });

  // Prepare update object
  const updateData: any = {};

  // If email is being updated, add it to the main update
  if (data.email) {
    updateData.email = data.email;
  }

  // Add metadata updates
  updateData.data = {};
  if (data.full_name) {
    updateData.data.full_name = data.full_name;
  }

  // Update user
  const { error } = await supabase.auth.updateUser(updateData);

  if (error) throw error;
  return true;
}

export async function getUserProgress(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('user_progress')
    .select(`
      *,
      subjects (
        name
      )
    `)
    .eq('user_id', userId);
    
  if (error) throw error;
  return data;
}

export async function getRecentResponses(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('responses')
    .select(`
      id,
      question_id,
      response,
      was_correct,
      created_at,
      questions (
        subject_id,
        subjects (
          name
        )
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (error) throw error;
  return data;
}

export async function getUserSubscription(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) throw error;
  if (!session?.user || session.user.id !== userId) throw new Error('User not found');

  return {
    subscription_status: session.user.user_metadata?.subscription_status || 'inactive',
    subscription_expires: session.user.user_metadata?.subscription_expires || null
  };
}

export async function updateUserSubscription(userId: string, subscriptionData: any) {
  const supabase = createServerActionClient({ cookies });

  // Update user metadata with subscription info
  const { error } = await supabase.auth.updateUser({
    data: {
      ...subscriptionData
    }
  });

  if (error) throw error;
  return true;
}

export async function getUserDashboardData(userId: string) {
  const supabase = createServerActionClient({ cookies });

  // Fetch user data from auth
  const { data: { session }, error: userError } = await supabase.auth.getSession();

  if (userError) {
    console.error('Session error in dashboard:', userError);
    throw userError;
  }

  if (!session?.user || session.user.id !== userId) {
    console.error('No session or user mismatch:', { sessionUserId: session?.user?.id, requestedUserId: userId });
    throw new Error('User not found');
  }

  const userData = {
    name: session.user.user_metadata?.full_name || 'Usuario',
    subscription_status: session.user.user_metadata?.subscription_status || 'inactive'
  };

  // For now, return just user data to avoid table issues
  console.log('Returning basic user data:', userData);

  return {
    user: userData,
    progress: [],
    responses: []
  };
}

export async function updateUserProgress(userId: string, subjectId: number, wasCorrect: boolean, timeSpentSeconds: number) {
  const supabase = createServerActionClient({ cookies });

  // First, try to get existing progress
  const { data: existingProgress, error: selectError } = await supabase
    .from('user_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('subject_id', subjectId)
    .single();

  if (selectError && selectError.code !== 'PGRST116') { // PGRST116 is "not found" error
    throw selectError;
  }

  const timeSpentHours = timeSpentSeconds / 3600; // Convert seconds to hours

  if (existingProgress) {
    // Update existing record
    const updateData = {
      correct_answers: wasCorrect
        ? existingProgress.correct_answers + 1
        : existingProgress.correct_answers,
      incorrect_answers: wasCorrect
        ? existingProgress.incorrect_answers
        : existingProgress.incorrect_answers + 1,
      hours: existingProgress.hours + timeSpentHours,
      last_updated: new Date().toISOString()
    };

    const { error: updateError } = await supabase
      .from('user_progress')
      .update(updateData)
      .eq('user_id', userId)
      .eq('subject_id', subjectId);

    if (updateError) throw updateError;
  } else {
    // Insert new record
    const insertData = {
      user_id: userId,
      subject_id: subjectId,
      correct_answers: wasCorrect ? 1 : 0,
      incorrect_answers: wasCorrect ? 0 : 1,
      user_level: 5, // Default level as requested
      hours: timeSpentHours,
      last_updated: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('user_progress')
      .insert(insertData);

    if (insertError) throw insertError;
  }

  return true;
}

export async function getUserProgressStats(userId: string) {
  console.log('Getting progress stats for user:', userId);

  // For now, return empty data to avoid table issues
  return {
    progress: [],
    responses: []
  };
}