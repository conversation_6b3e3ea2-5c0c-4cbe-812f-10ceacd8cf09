"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function getUserProfile(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
    
  if (error) throw error;
  return data;
}

export async function updateUserProfile(userId: string, data: any) {
  const supabase = createServerActionClient({ cookies });
  const { error } = await supabase
    .from('users')
    .update(data)
    .eq('id', userId);
    
  if (error) throw error;
  return true;
}

export async function getUserProgress(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('user_progress')
    .select(`
      *,
      subjects (
        name
      )
    `)
    .eq('user_id', userId);
    
  if (error) throw error;
  return data;
}

export async function getRecentResponses(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('responses')
    .select(`
      id,
      question_id,
      response,
      was_correct,
      created_at,
      questions (
        subject_id,
        subjects (
          name
        )
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (error) throw error;
  return data;
}

export async function getUserSubscription(userId: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('users')
    .select('subscription_status, subscription_expires')
    .eq('id', userId)
    .single();

  if (error) throw error;
  return data;
}

export async function updateUserSubscription(userId: string, subscriptionData: any) {
  const supabase = createServerActionClient({ cookies });
  const { error } = await supabase
    .from('users')
    .update(subscriptionData)
    .eq('id', userId);

  if (error) throw error;
  return true;
}

export async function getUserDashboardData(userId: string) {
  const supabase = createServerActionClient({ cookies });

  // Fetch user data
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('name, subscription_status')
    .eq('id', userId)
    .single();

  if (userError) throw userError;

  // Fetch user progress
  const { data: progress, error: progressError } = await supabase
    .from('user_progress')
    .select('subject_id, correct_answers, incorrect_answers, user_level, hours')
    .eq('user_id', userId);

  if (progressError) throw progressError;

  // Fetch recent responses with subject info
  const { data: responses, error: responsesError } = await supabase
    .from('responses')
    .select(`
      id,
      question_id,
      response,
      was_correct,
      created_at,
      questions!inner (
        subject_id,
        subjects!inner (
          name
        )
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (responsesError) throw responsesError;

  return {
    user,
    progress,
    responses
  };
}