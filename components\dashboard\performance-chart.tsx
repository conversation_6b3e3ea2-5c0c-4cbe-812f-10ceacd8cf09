"use client";

import { useState, useEffect } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { useTheme } from 'next-themes';

// Mock data for the chart
const data = [
  { date: '01/05', matemática: 65, física: 40, química: 30, historia: 25 },
  { date: '02/05', matemática: 60, física: 35, química: 40, historia: 35 },
  { date: '03/05', matemática: 70, física: 45, química: 45, historia: 40 },
  { date: '04/05', matemática: 75, física: 50, química: 40, historia: 45 },
  { date: '05/05', matemática: 80, física: 60, química: 50, historia: 55 },
  { date: '06/05', matemática: 85, física: 70, química: 60, historia: 60 },
  { date: '07/05', matemática: 90, física: 65, química: 65, historia: 65 },
];

export function PerformanceChart() {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  
  // Theme-dependent colors
  const [colors, setColors] = useState({
    matemática: '#2563eb', // blue-600
    física: '#9333ea',     // purple-600
    química: '#16a34a',    // green-600
    historia: '#d97706',   // amber-600
    grid: '#e5e7eb',       // gray-200
    text: '#6b7280',       // gray-500
  });

  useEffect(() => {
    setMounted(true);
    
    // Update colors based on theme
    if (theme === 'dark') {
      setColors({
        matemática: '#3b82f6', // blue-500
        física: '#a855f7',     // purple-500
        química: '#22c55e',    // green-500
        historia: '#f59e0b',   // amber-500
        grid: '#374151',       // gray-700
        text: '#9ca3af',       // gray-400
      });
    } else {
      setColors({
        matemática: '#2563eb', // blue-600
        física: '#9333ea',     // purple-600
        química: '#16a34a',    // green-600
        historia: '#d97706',   // amber-600
        grid: '#e5e7eb',       // gray-200
        text: '#6b7280',       // gray-500
      });
    }
  }, [theme]);

  if (!mounted) {
    return null;
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data} margin={{ top: 10, right: 20, left: -10, bottom: 0 }}>
        <defs>
          <linearGradient id="matemática" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colors.matemática} stopOpacity={0.3} />
            <stop offset="95%" stopColor={colors.matemática} stopOpacity={0} />
          </linearGradient>
          <linearGradient id="física" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colors.física} stopOpacity={0.3} />
            <stop offset="95%" stopColor={colors.física} stopOpacity={0} />
          </linearGradient>
          <linearGradient id="química" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colors.química} stopOpacity={0.3} />
            <stop offset="95%" stopColor={colors.química} stopOpacity={0} />
          </linearGradient>
          <linearGradient id="historia" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colors.historia} stopOpacity={0.3} />
            <stop offset="95%" stopColor={colors.historia} stopOpacity={0} />
          </linearGradient>
        </defs>
        <XAxis 
          dataKey="date" 
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
        />
        <YAxis 
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
          domain={[0, 100]}
          ticks={[0, 25, 50, 75, 100]}
        />
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke={colors.grid}
          vertical={false}
        />
        <Tooltip 
          contentStyle={{ 
            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
            borderColor: theme === 'dark' ? '#374151' : '#e5e7eb',
            borderRadius: '0.375rem',
            fontSize: '0.875rem',
            color: theme === 'dark' ? '#e5e7eb' : '#374151'
          }}
        />
        <Area 
          type="monotone" 
          dataKey="matemática" 
          stroke={colors.matemática} 
          fill="url(#matemática)" 
          strokeWidth={2}
        />
        <Area 
          type="monotone" 
          dataKey="física" 
          stroke={colors.física} 
          fill="url(#física)"
          strokeWidth={2}
        />
        <Area 
          type="monotone" 
          dataKey="química" 
          stroke={colors.química} 
          fill="url(#química)"
          strokeWidth={2}
        />
        <Area 
          type="monotone" 
          dataKey="historia" 
          stroke={colors.historia} 
          fill="url(#historia)"
          strokeWidth={2}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
}