"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardSidebar } from '@/components/dashboard/sidebar';
import { DashboardHeader } from '@/components/dashboard/header';
import { supabase, getSession } from '@/lib/supabase/client';
import { Loader2 } from 'lucide-react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    console.log("Layout: Starting auth check...");

    const checkSession = async () => {
      try {
        // First, try to get the current session
        const session = await getSession();
        console.log("Layout: Session check result:", { session: !!session });

        if (session) {
          console.log("Layout: Session found, user authenticated");
          setIsAuthenticated(true);
          setIsLoading(false);
        } else {
          console.log("Layout: No session found, redirecting to login");
          router.replace('/login');
        }
      } catch (err) {
        console.error("Layout: Error checking session:", err);
        router.replace('/login');
      }
    };

    checkSession();

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Layout: Auth state changed:", { event, session: !!session });

      if (event === 'SIGNED_OUT' || !session) {
        console.log("Layout: User signed out or no session");
        setIsAuthenticated(false);
        router.replace('/login');
      } else if (event === 'SIGNED_IN' && session) {
        console.log("Layout: User signed in");
        setIsAuthenticated(true);
        setIsLoading(false);
      } else if (event === 'TOKEN_REFRESHED' && session) {
        console.log("Layout: Token refreshed");
        setIsAuthenticated(true);
        setIsLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen">
      <DashboardSidebar />
      <div className="flex flex-col flex-1">
        <DashboardHeader />
        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}