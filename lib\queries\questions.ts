// Client-side queries for reading questions data
// DIRECT SUPABASE CALLS - All data comes from client

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

const supabase = createClientComponentClient();

export async function getQuestionCount(subjectId: number): Promise<number> {
  try {
    const { count } = await supabase
      .from('questions')
      .select('*', { count: 'exact', head: true })
      .eq('subject_id', subjectId);

    return count || 0;
  } catch (error) {
    console.error('Error getting question count:', error);
    return 0;
  }
}

export async function getFirstQuestion(subjectId: number) {
  try {
    const { data: questions, error } = await supabase
      .from('questions')
      .select('*')
      .eq('subject_id', subjectId)
      .order('id', { ascending: true })
      .limit(1);

    if (error) throw error;

    if (questions && questions.length > 0) {
      return questions[0];
    }

    return null;
  } catch (error) {
    console.error('Error getting first question:', error);
    return null;
  }
}

// Get user progress data for charts
export async function getUserProgressData(userId: string) {
  try {
    // Get user responses grouped by date and subject
    const { data: responses, error } = await supabase
      .from('responses')
      .select(`
        created_at,
        was_correct,
        question_id
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    if (error) throw error;

    // Get questions and subjects separately
    if (responses && responses.length > 0) {
      const questionIds = responses.map((r: any) => r.question_id);

      const { data: questions } = await supabase
        .from('questions')
        .select('id, subject_id')
        .in('id', questionIds);

      const { data: subjects } = await supabase
        .from('subjects')
        .select('id, name');

      // Combine the data
      const enrichedResponses = responses.map((r: any) => {
        const question = questions?.find((q: any) => q.id === r.question_id);
        const subject = subjects?.find((s: any) => s.id === question?.subject_id);

        return {
          ...r,
          subject_name: subject?.name?.toLowerCase() || 'desconocido'
        };
      });

      // Process data to create chart format
      const processedData = processResponsesForChart(enrichedResponses);
      return processedData;
    }

    return [];
  } catch (error) {
    console.error('Error getting user progress data:', error);
    return [];
  }
}

// Get user progress stats for the progress page with date filtering
export async function getUserProgressStats(userId: string, dateRange?: string, specificMonth?: string) {
  try {
    // Calculate date filter
    let dateFilter = null;
    if (dateRange && dateRange !== 'all-time') {
      const now = new Date();
      if (dateRange === 'last-week') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = weekAgo.toISOString();
      } else if (dateRange === 'last-month') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = monthAgo.toISOString();
      }
    } else if (specificMonth) {
      // Format: "2024-05" for May 2024
      const [year, month] = specificMonth.split('-');
      const startOfMonth = new Date(parseInt(year), parseInt(month) - 1, 1);
      const endOfMonth = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59);
      dateFilter = { start: startOfMonth.toISOString(), end: endOfMonth.toISOString() };
    }

    // Get user progress
    const { data: progressData, error: progressError } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', userId);

    if (progressError) {
      console.error('Progress stats error:', progressError);
    }

    // Get subjects separately
    const { data: subjects } = await supabase
      .from('subjects')
      .select('*');

    // Combine progress with subjects
    const enrichedProgress = (progressData || []).map((p: any) => {
      const subject = subjects?.find((s: any) => s.id === p.subject_id);
      return {
        ...p,
        subjects: subject
      };
    });

    // Get responses with date filtering
    let responsesQuery = supabase
      .from('responses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply date filter to responses
    if (dateFilter) {
      if (typeof dateFilter === 'string') {
        responsesQuery = responsesQuery.gte('created_at', dateFilter);
      } else {
        responsesQuery = responsesQuery
          .gte('created_at', dateFilter.start)
          .lte('created_at', dateFilter.end);
      }
    }

    const { data: responsesData, error: responsesError } = await responsesQuery;

    if (responsesError) {
      console.error('Responses stats error:', responsesError);
    }

    // Get questions and subjects for responses
    if (responsesData && responsesData.length > 0) {
      const questionIds = responsesData.map((r: any) => r.question_id);

      const { data: questions } = await supabase
        .from('questions')
        .select('*')
        .in('id', questionIds);

      // Combine responses with questions
      const enrichedResponses = responsesData.map((r: any) => {
        const question = questions?.find((q: any) => q.id === r.question_id);
        return {
          ...r,
          questions: question
        };
      });

      // Filter progress data based on responses if we have date filtering
      let filteredProgressData = enrichedProgress;
      if (dateFilter && enrichedResponses) {
        // Get unique subject IDs from filtered responses
        const subjectIds = Array.from(new Set(enrichedResponses.map((r: any) => r.questions?.subject_id).filter(Boolean)));
        filteredProgressData = enrichedProgress.filter((p: any) => subjectIds.includes(p.subject_id));
      }

      return {
        progress: filteredProgressData,
        responses: enrichedResponses
      };
    }

    return {
      progress: enrichedProgress,
      responses: responsesData || []
    };
  } catch (error) {
    console.error('Error getting user progress stats:', error);
    return {
      progress: [],
      responses: []
    };
  }
}

function processResponsesForChart(responses: any[]) {
  // Group responses by date
  const dateGroups: { [key: string]: { [subject: string]: { correct: number; total: number } } } = {};

  responses.forEach(response => {
    const date = new Date(response.created_at).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit'
    });
    const subjectName = response.subject_name || 'desconocido';

    if (!dateGroups[date]) {
      dateGroups[date] = {};
    }

    if (!dateGroups[date][subjectName]) {
      dateGroups[date][subjectName] = { correct: 0, total: 0 };
    }

    dateGroups[date][subjectName].total++;
    if (response.was_correct) {
      dateGroups[date][subjectName].correct++;
    }
  });

  // Convert to chart format with cumulative percentages
  const chartData: any[] = [];
  const cumulativeStats: { [subject: string]: { correct: number; total: number } } = {};

  Object.keys(dateGroups).forEach(date => {
    const dayData: any = { date };

    Object.keys(dateGroups[date]).forEach(subject => {
      if (!cumulativeStats[subject]) {
        cumulativeStats[subject] = { correct: 0, total: 0 };
      }

      cumulativeStats[subject].correct += dateGroups[date][subject].correct;
      cumulativeStats[subject].total += dateGroups[date][subject].total;

      // Calculate percentage
      const percentage = cumulativeStats[subject].total > 0
        ? Math.round((cumulativeStats[subject].correct / cumulativeStats[subject].total) * 100)
        : 0;

      dayData[subject] = percentage;
    });

    chartData.push(dayData);
  });

  return chartData;
}

// NOTE: submitAnswer functionality moved to /lib/actions/questions.ts as server action

