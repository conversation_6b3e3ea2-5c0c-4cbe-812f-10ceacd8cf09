// Client-side queries for reading questions data
// NO DIRECT SUPABASE CALLS - All data comes from server actions

import {
  getQuestionCount as getQuestionCountAction,
  getFirstQuestion as getFirstQuestionAction,
  getUserProgressData as getUserProgressDataAction,
  getUserProgressStats as getUserProgressStatsAction
} from '@/lib/actions/questions';

export async function getQuestionCount(subjectId: number): Promise<number> {
  try {
    return await getQuestionCountAction(subjectId);
  } catch (error) {
    console.error('Error getting question count:', error);
    return 0;
  }
}

export async function getFirstQuestion(subjectId: number) {
  try {
    return await getFirstQuestionAction(subjectId);
  } catch (error) {
    console.error('Error getting first question:', error);
    return null;
  }
}

// Get user progress data for charts
export async function getUserProgressData(userId: string) {
  try {
    return await getUserProgressDataAction(userId);
  } catch (error) {
    console.error('Error getting user progress data:', error);
    return [];
  }
}

// Get user progress stats for the progress page with date filtering
export async function getUserProgressStats(userId: string, dateRange?: string, specificMonth?: string) {
  try {
    return await getUserProgressStatsAction(userId, dateRange, specificMonth);
  } catch (error) {
    console.error('Error getting user progress stats:', error);
    return {
      progress: [],
      responses: []
    };
  }
}

// NOTE: submitAnswer functionality moved to /lib/actions/questions.ts as server action

