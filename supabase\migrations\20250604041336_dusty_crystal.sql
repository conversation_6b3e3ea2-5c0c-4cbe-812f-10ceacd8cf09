/*
  # Create institution table

  1. New Tables
    - `institution`
      - `id` (integer, primary key)
      - `name` (text, not null)
  2. Security
    - Enable RLS on `institution` table
    - Add policy for public read access
*/

CREATE TABLE IF NOT EXISTS institutions (
  id serial PRIMARY KEY,
  name text NOT NULL
);

ALTER TABLE institutions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read institutions"
  ON institutions
  FOR SELECT
  TO anon, authenticated
  USING (true);