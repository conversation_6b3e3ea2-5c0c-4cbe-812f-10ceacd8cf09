"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function getInstitutions() {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('institutions')
    .select('*');
    
  if (error) throw error;
  return data;
}

export async function getSubjects() {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase
    .from('subjects')
    .select('*');
    
  if (error) throw error;
  return data;
}

export async function getSubjectsWithDefaults() {
  const subjects = await getSubjects();
  
  // Add default icon and color for each subject
  return subjects.map((subject) => ({
    ...subject,
    icon: 'BookOpen',
    color: 'bg-blue-100 dark:bg-blue-950',
    description: 'Preparación completa para el examen de admisión'
  }));
}
