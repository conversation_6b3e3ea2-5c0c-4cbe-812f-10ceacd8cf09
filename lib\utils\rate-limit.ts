// Rate limit utilities for Supabase Auth
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Cache for session to avoid repeated calls
let sessionCache: any = null;
let lastSessionFetch = 0;
const SESSION_CACHE_DURATION = 30000; // 30 seconds

// Rate limit tracking
let lastAuthCall = 0;
const MIN_AUTH_INTERVAL = 1000; // 1 second between auth calls

export async function getSessionWithRateLimit() {
  const now = Date.now();
  
  // Return cached session if it's still valid
  if (sessionCache && (now - lastSessionFetch) < SESSION_CACHE_DURATION) {
    return sessionCache;
  }
  
  // Enforce minimum interval between auth calls
  const timeSinceLastCall = now - lastAuthCall;
  if (timeSinceLastCall < MIN_AUTH_INTERVAL) {
    const waitTime = MIN_AUTH_INTERVAL - timeSinceLastCall;
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  
  try {
    lastAuthCall = Date.now();
    const supabase = createClientComponentClient();
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      if (error.message?.includes('rate limit') || error.status === 429) {
        console.warn('Rate limit reached, returning cached session');
        return sessionCache;
      }
      throw error;
    }
    
    // Update cache
    sessionCache = session;
    lastSessionFetch = Date.now();
    
    return session;
  } catch (error: any) {
    if (error.message?.includes('rate limit') || error.status === 429) {
      console.warn('Rate limit reached, returning cached session');
      return sessionCache;
    }
    throw error;
  }
}

export function clearSessionCache() {
  sessionCache = null;
  lastSessionFetch = 0;
}

export async function withRateLimit<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T> {
  try {
    return await operation();
  } catch (error: any) {
    if (error.message?.includes('rate limit') || error.status === 429) {
      console.warn('Rate limit reached, using fallback or retrying later');
      if (fallback !== undefined) {
        return fallback;
      }
      // Wait and retry once
      await new Promise(resolve => setTimeout(resolve, 2000));
      try {
        return await operation();
      } catch (retryError: any) {
        if (retryError.message?.includes('rate limit') || retryError.status === 429) {
          throw new Error('Rate limit exceeded. Please wait a moment before trying again.');
        }
        throw retryError;
      }
    }
    throw error;
  }
}
