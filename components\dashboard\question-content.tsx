"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft, ArrowRight, Clock, Loader2, Zap } from 'lucide-react';
import { SUBJECTS } from '@/lib/constants';
import { QuestionFeedback } from '@/components/dashboard/question-feedback';
import { MathRenderer } from '@/components/ui/math-renderer';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';
import { getQuestionCount, getFirstQuestion, submitAnswerClient } from '@/lib/queries/questions';
import { supabase } from '@/lib/supabase/client';

interface QuestionContentProps {
  subjectId: number;
}

interface Question {
  id: number;
  question: string;
  options: string[];
  correct_answer: number;
  solution: string;
  image: string | null;
  question_image_url: string | null;
  solution_image_url: string | null;
  difficulty: number;
}

export function QuestionContent({ subjectId }: QuestionContentProps) {
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('Questions');
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [isAnswered, setIsAnswered] = useState(false);
  const [userScore, setUserScore] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const subject = SUBJECTS.find(s => s.id === subjectId);

  useEffect(() => {
    fetchQuestion();
  }, [subjectId]);

  useEffect(() => {
    if (!currentQuestion) return;

    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);
    
    setIntervalId(interval);
    setSelectedOption(null);
    setIsAnswered(false);
    setTimeElapsed(0);
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentQuestion]);

  async function fetchQuestion() {
    setIsLoading(true);
    try {
      const count = await getQuestionCount(subjectId);
      setTotalQuestions(count);

      const question = await getFirstQuestion(subjectId);
      if (question) {
        setCurrentQuestion(question);
      } else {
        toast({
          title: t('noQuestions'),
          description: t('noQuestionsDescription'),
        });
        router.push('/subjects');
      }
    } catch (error) {
      console.error('Error fetching question:', error);
      toast({
        variant: "destructive",
        title: t('errorTitle'),
        description: t('errorDescription'),
      });
    } finally {
      setIsLoading(false);
    }
  }

  const handleOptionSelect = (optionIndex: number) => {
    if (isAnswered) return;
    setSelectedOption(optionIndex);
  };

  async function handleSubmitAnswer() {
    if (selectedOption === null || isAnswered || !currentQuestion) return;
    
    if (intervalId) clearInterval(intervalId);
    
    const isCorrect = selectedOption === currentQuestion.correct_answer;
    
    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.user) {
        console.error('Session error:', sessionError);
        return;
      }

      await submitAnswerClient(currentQuestion.id, session.user.id, selectedOption.toString(), isCorrect, subjectId, timeElapsed);

      if (isCorrect) {
        setUserScore(prev => prev + 1);
      }
      
      setIsAnswered(true);
      setCurrentQuestionIndex(prev => prev + 1);
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        variant: "destructive",
        title: t('errorTitle'),
        description: t('errorSubmitting'),
      });
    }
  }

  const handleNextQuestion = () => {
    router.push('/subjects');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!currentQuestion || !subject) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <p className="text-lg mb-4">{t('noQuestionsDescription')}</p>
        <Button onClick={() => router.push('/subjects')}>
          {t('backToSubjects')}
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <button 
          onClick={() => router.push('/subjects')}
          className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          {t('backToSubjects')}
        </button>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Clock className="h-4 w-4" />
            <span>{formatTime(timeElapsed)}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Zap className="h-4 w-4" />
            <span>{userScore} {t('points')}</span>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-xl font-bold">{subject.name}</h1>
          <span className="text-sm">
            {t('questionNumber', { number: currentQuestionIndex + 1 })} de {totalQuestions}
          </span>
        </div>
        <Progress 
          value={((currentQuestionIndex + 1) / totalQuestions) * 100} 
          className="h-2" 
        />
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">{t('questionNumber', { number: currentQuestionIndex + 1 })}</CardTitle>
          <CardDescription>
            {t('difficulty', { level: currentQuestion.difficulty })}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <MathRenderer 
              content={currentQuestion.question}
              className="text-base leading-relaxed"
            />
          </div>
          
          {(currentQuestion.question_image_url || currentQuestion.image) && (
            <div className="mb-6">
              <img 
                src={currentQuestion.question_image_url || currentQuestion.image || ''} 
                alt={t('questionImage')}
                className="max-w-full rounded-md shadow-sm border border-gray-200 dark:border-gray-700" 
              />
            </div>
          )}
          
          <RadioGroup
            value={selectedOption !== null ? selectedOption.toString() : ''}
            className="space-y-3"
            onValueChange={(value) => handleOptionSelect(parseInt(value))}
            disabled={isAnswered}
          >
            {currentQuestion.options.map((option, index) => (
              <div 
                key={index} 
                className={`flex items-start space-x-3 rounded-lg border p-4 cursor-pointer transition-all duration-200 ${
                  isAnswered && index === currentQuestion.correct_answer
                    ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-900 shadow-sm'
                    : isAnswered && index === selectedOption && index !== currentQuestion.correct_answer
                      ? 'bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-900 shadow-sm'
                      : selectedOption === index
                        ? 'bg-blue-50 border-blue-200 dark:bg-blue-950/30 dark:border-blue-900'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-900/50 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => handleOptionSelect(index)}
              >
                <RadioGroupItem
                  value={index.toString()}
                  id={`option-${index}`}
                  className={`mt-1 ${
                    isAnswered && index === currentQuestion.correct_answer
                      ? 'text-green-600 border-green-600'
                      : isAnswered && index === selectedOption && index !== currentQuestion.correct_answer
                        ? 'text-red-600 border-red-600'
                        : selectedOption === index
                          ? 'text-blue-600 border-blue-600'
                          : ''
                  }`}
                  disabled={isAnswered}
                />
                <div className="flex-1">
                  <MathRenderer 
                    content={option}
                    className="text-sm leading-relaxed"
                  />
                </div>
              </div>
            ))}
          </RadioGroup>
          
          {isAnswered && (
            <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
              <h3 className="text-lg font-semibold mb-4 text-blue-900 dark:text-blue-100 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                {t('solutionExplained')}
              </h3>
              <MathRenderer 
                content={currentQuestion.solution}
                className="text-sm leading-relaxed"
              />
              {currentQuestion.solution_image_url && (
                <div className="mt-4">
                  <img 
                    src={currentQuestion.solution_image_url} 
                    alt={t('solutionImage')}
                    className="max-w-full rounded-md shadow-sm border border-gray-200 dark:border-gray-700" 
                  />
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          {!isAnswered ? (
            <div className="flex justify-end w-full">
              <Button
                onClick={handleSubmitAnswer}
                disabled={selectedOption === null}
                className="px-8 py-2 font-medium"
              >
                {t('answer')}
              </Button>
            </div>
          ) : (
            <div className="flex justify-between w-full items-center">
              <QuestionFeedback 
                isCorrect={selectedOption === currentQuestion.correct_answer}
              />
              <Button onClick={handleNextQuestion} className="px-8 py-2 font-medium">
                {t('finish')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}