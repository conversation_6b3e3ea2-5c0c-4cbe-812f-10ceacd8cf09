"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft, ArrowRight, Clock, Loader2, Zap } from 'lucide-react';
import { SUBJECTS } from '@/lib/constants';
import { QuestionFeedback } from '@/components/dashboard/question-feedback';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import { getQuestionCount, getFirstQuestion, submitAnswer } from '@/lib/actions/questions';
import { supabase } from '@/lib/supabase/client';

interface QuestionContentProps {
  subjectId: number;
}

interface Question {
  id: number;
  question: string;
  options: Array<{
    id: string;
    text: string;
    correct: boolean;
  }>;
  solution: string;
  image: string | null;
  difficulty: number;
}

export function QuestionContent({ subjectId }: QuestionContentProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isAnswered, setIsAnswered] = useState(false);
  const [userScore, setUserScore] = useState(0);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const subject = SUBJECTS.find(s => s.id === subjectId);

  useEffect(() => {
    fetchQuestion();
  }, [subjectId]);

  useEffect(() => {
    if (!currentQuestion) return;

    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
    }, 1000);
    
    setIntervalId(interval);
    setSelectedOption(null);
    setIsAnswered(false);
    setTimeElapsed(0);
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentQuestion]);

  async function fetchQuestion() {
    setIsLoading(true);
    try {
      const count = await getQuestionCount(subjectId);
      setTotalQuestions(count);

      const question = await getFirstQuestion(subjectId);
      if (question) {
        setCurrentQuestion(question);
      } else {
        toast({
          title: "No hay preguntas",
          description: "No hay preguntas disponibles para esta materia.",
        });
        router.push('/subjects');
      }
    } catch (error) {
      console.error('Error fetching question:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "No se pudo cargar la pregunta.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  const handleOptionSelect = (optionId: string) => {
    if (isAnswered) return;
    setSelectedOption(optionId);
  };

  async function handleSubmitAnswer() {
    if (!selectedOption || isAnswered || !currentQuestion) return;
    
    if (intervalId) clearInterval(intervalId);
    
    const selectedAnswerOption = currentQuestion.options.find(opt => opt.id === selectedOption);
    const isCorrect = selectedAnswerOption?.correct || false;
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return;

      await submitAnswer(currentQuestion.id, session.user.id, selectedOption, isCorrect);

      if (isCorrect) {
        setUserScore(prev => prev + 1);
      }
      
      setIsAnswered(true);
      setCurrentQuestionIndex(prev => prev + 1);
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "No se pudo registrar tu respuesta.",
      });
    }
  }

  const handleNextQuestion = () => {
    router.push('/subjects');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!currentQuestion || !subject) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-center">
        <p className="text-lg mb-4">No hay preguntas disponibles para esta materia.</p>
        <Button onClick={() => router.push('/subjects')}>
          Volver a materias
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <button 
          onClick={() => router.push('/subjects')}
          className="flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Volver a materias
        </button>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Clock className="h-4 w-4" />
            <span>{formatTime(timeElapsed)}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Zap className="h-4 w-4" />
            <span>{userScore} puntos</span>
          </div>
        </div>
      </div>
      
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-xl font-bold">{subject.name}</h1>
          <span className="text-sm">
            Pregunta {currentQuestionIndex + 1} de {totalQuestions}
          </span>
        </div>
        <Progress 
          value={((currentQuestionIndex + 1) / totalQuestions) * 100} 
          className="h-2" 
        />
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Pregunta {currentQuestionIndex + 1}</CardTitle>
          <CardDescription>
            Dificultad: {currentQuestion.difficulty}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            className="prose dark:prose-invert max-w-none mb-6"
            dangerouslySetInnerHTML={{ __html: currentQuestion.question }}
          />
          
          {currentQuestion.image && (
            <div className="mb-6">
              <img 
                src={currentQuestion.image} 
                alt="Imagen de la pregunta"
                className="max-w-full rounded-md" 
              />
            </div>
          )}
          
          <RadioGroup
            value={selectedOption || ''}
            className="space-y-3"
            onValueChange={handleOptionSelect}
            disabled={isAnswered}
          >
            {currentQuestion.options.map((option) => (
              <div 
                key={option.id} 
                className={`flex items-center space-x-3 rounded-lg border p-4 cursor-pointer ${
                  isAnswered && option.correct
                    ? 'bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-900'
                    : isAnswered && option.id === selectedOption
                      ? 'bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-900'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-900/50'
                }`}
                onClick={() => handleOptionSelect(option.id)}
              >
                <RadioGroupItem
                  value={option.id}
                  id={`option-${option.id}`}
                  className={`${
                    isAnswered && option.correct
                      ? 'text-green-600 border-green-600'
                      : isAnswered && option.id === selectedOption
                        ? 'text-red-600 border-red-600'
                        : ''
                  }`}
                  disabled={isAnswered}
                />
                <label
                  htmlFor={`option-${option.id}`}
                  className="flex-1 font-medium text-gray-900 dark:text-gray-100"
                >
                  {option.text}
                </label>
              </div>
            ))}
          </RadioGroup>
          
          {isAnswered && (
            <div className="mt-8">
              <h3 className="text-lg font-medium mb-2">Solución:</h3>
              <div 
                className="prose dark:prose-invert max-w-none p-4 bg-gray-50 dark:bg-gray-900 rounded-md"
                dangerouslySetInnerHTML={{ __html: currentQuestion.solution }}
              />
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          {!isAnswered ? (
            <div className="flex justify-end w-full">
              <Button
                onClick={handleSubmitAnswer}
                disabled={!selectedOption}
              >
                Responder
              </Button>
            </div>
          ) : (
            <div className="flex justify-between w-full items-center">
              <QuestionFeedback 
                isCorrect={currentQuestion.options.find(opt => opt.id === selectedOption)?.correct || false}
              />
              <Button onClick={handleNextQuestion}>
                Finalizar
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}