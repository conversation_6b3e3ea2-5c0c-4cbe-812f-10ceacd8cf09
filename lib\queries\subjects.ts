// Client-side queries for reading subjects data
// DIRECT SUPABASE CALLS - All data comes from client

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

const supabase = createClientComponentClient();

interface Institution {
  id: string;
  name: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
}

export async function getInstitutions(): Promise<Institution[]> {
  try {
    const { data, error } = await supabase
      .from('institutions')
      .select('*');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting institutions:', error);
    return [];
  }
}

export async function getSubjects(): Promise<Subject[]> {
  try {
    const { data, error } = await supabase
      .from('subjects')
      .select('*');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting subjects:', error);
    return [];
  }
}

export async function getSubjectsWithDefaults() {
  const subjects = await getSubjects();

  // Add default icon and color for each subject
  return subjects.map((subject) => ({
    ...subject,
    icon: 'BookOpen',
    color: 'bg-blue-100 dark:bg-blue-950',
    description: 'Preparación completa para el examen de admisión'
  }));
}