// Client-side queries for reading subjects data
// NO DIRECT SUPABASE CALLS - All data comes from server actions

import { getInstitutions as getInstitutionsAction, getSubjects as getSubjectsAction } from '@/lib/actions/subjects';

interface Institution {
  id: string;
  name: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
}

export async function getInstitutions(): Promise<Institution[]> {
  try {
    return await getInstitutionsAction();
  } catch (error) {
    console.error('Error getting institutions:', error);
    return [];
  }
}

export async function getSubjects(): Promise<Subject[]> {
  try {
    return await getSubjectsAction();
  } catch (error) {
    console.error('Error getting subjects:', error);
    return [];
  }
}

export async function getSubjectsWithDefaults() {
  const subjects = await getSubjects();

  // Add default icon and color for each subject
  return subjects.map((subject) => ({
    ...subject,
    icon: 'BookOpen',
    color: 'bg-blue-100 dark:bg-blue-950',
    description: 'Preparación completa para el examen de admisión'
  }));
}