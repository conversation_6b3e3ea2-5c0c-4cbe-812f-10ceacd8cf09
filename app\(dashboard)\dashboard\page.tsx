"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  ArrowRight, 
  BookOpen, 
  CheckCircle, 
  Clock, 
  LineChart, 
  ThumbsUp, 
  Zap,
  Award,
  BookMarked
} from 'lucide-react';
import { SUBJECTS } from '@/lib/constants';
import { supabase } from '@/lib/supabase/client';
import { RecentActivityCard } from '@/components/dashboard/recent-activity-card';
import { PerformanceChart } from '@/components/dashboard/performance-chart';

interface UserData {
  name: string;
  subscription_status: string;
}

interface UserProgress {
  subject_id: number;
  correct_answers: number;
  incorrect_answers: number;
  user_level: number;
  hours: number;
}

interface Response {
  id: number;
  question_id: number;
  response: string;
  was_correct: boolean;
  created_at: string;
  questions: {
    subject_id: number;
    subjects: {
      name: string;
    }
  }
}

export default function DashboardPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userProgress, setUserProgress] = useState<UserProgress[]>([]);
  const [recentResponses, setRecentResponses] = useState<Response[]>([]);
  
  useEffect(() => {
    console.log("DASHBOARD 1")
    async function fetchData() {
      console.log('Starting dashboard data fetch with client...');

      try {
        console.log("DASHBOARD 33")
        console.log('Getting session with client...');
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        console.log('Session result:', session);
        console.log('Session error:', sessionError);

        if (session?.user) {
          console.log('User found:', session.user);

          // Set user data from session
          const userData = {
            name: session.user.user_metadata?.full_name || 'Usuario',
            subscription_status: session.user.user_metadata?.subscription_status || 'inactive'
          };

          console.log('Setting user data:', userData);
          setUserData(userData);

          // Try to fetch progress data
          try {
            console.log('Fetching user progress...');
            const { data: progress, error: progressError } = await supabase
              .from('user_progress')
              .select('subject_id, correct_answers, incorrect_answers, user_level, hours')
              .eq('user_id', session.user.id);

            console.log('Progress result:', progress);
            console.log('Progress error:', progressError);
            setUserProgress(progress || []);
          } catch (progressErr) {
            console.error('Progress fetch error:', progressErr);
            setUserProgress([]);
          }

          // Try to fetch responses data
          try {
            console.log('Fetching responses...');
            const { data: responses, error: responsesError } = await supabase
              .from('responses')
              .select(`
                id,
                question_id,
                response,
                was_correct,
                created_at
              `)
              .eq('user_id', session.user.id)
              .order('created_at', { ascending: false })
              .limit(5);

            console.log('Responses result:', responses);
            console.log('Responses error:', responsesError);
            setRecentResponses(responses || [] as any);
          } catch (responsesErr) {
            console.error('Responses fetch error:', responsesErr);
            setRecentResponses([]);
          }

          console.log('All data set successfully');
        } else {
          console.log('No session found in dashboard');
          // Redirect to login if no session
          window.location.href = '/login';
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        console.log('Setting loading to false');
        setIsLoading(false);
      }
    }

    fetchData();
  }, []);

  // Calculate statistics
  const totalAnswers = userProgress.reduce((sum, p) => sum + p.correct_answers + p.incorrect_answers, 0);
  const totalCorrect = userProgress.reduce((sum, p) => sum + p.correct_answers, 0);
  const totalHours = userProgress.reduce((sum, p) => sum + p.hours, 0);
  const averageLevel = userProgress.length > 0 
    ? (userProgress.reduce((sum, p) => sum + p.user_level, 0) / userProgress.length).toFixed(1)
    : "0.0";

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            ¡Bienvenido, {userData?.name || 'Estudiante'}!
          </h2>
          <p className="text-muted-foreground">
            Aquí tienes un resumen de tu progreso y actividades recientes.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/subjects">
            <Button className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Explorar materias
            </Button>
          </Link>
        </div>
      </div>

      {userData?.subscription_status !== 'active' && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border-blue-100 dark:border-blue-900">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  <h3 className="font-medium text-lg">Desbloquea todo el contenido</h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 md:max-w-md">
                  Actualiza a un plan premium para acceder a todas las materias, preguntas y contenido exclusivo.
                </p>
              </div>
              <Link href="/subscription">
                <Button>Ver planes</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 md:gap-8 grid-cols-1 md:grid-cols-4 lg:grid-cols-6">
        <div className="col-span-1 md:col-span-3 lg:col-span-4 space-y-4 md:space-y-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Tu progreso general</CardTitle>
              <CardDescription>
                Resumen de tu rendimiento en todas las materias
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <PerformanceChart />
              </div>
            </CardContent>
          </Card>
          
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Materias recientes</CardTitle>
                <CardDescription>
                  Continúa donde lo dejaste
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-4">
                  {userProgress.slice(0, 3).map((progress) => {
                    const subject = SUBJECTS.find(s => s.id === progress.subject_id);
                    const totalAnswers = progress.correct_answers + progress.incorrect_answers;
                    const progressPercent = totalAnswers > 0 
                      ? (progress.correct_answers / totalAnswers) * 100 
                      : 0;

                    return (
                      <div key={progress.subject_id} className="flex items-center gap-4">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${subject?.color || 'bg-gray-100'}`}>
                          <BookMarked className="h-5 w-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">{subject?.name || 'Materia'}</p>
                          <div className="flex items-center justify-between mt-1">
                            <Progress value={progressPercent} className="h-1.5 w-2/3" />
                            <span className="text-xs text-muted-foreground">
                              {Math.round(progressPercent)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
              <CardFooter className="pt-2">
                <Link href="/subjects" className="w-full">
                  <Button variant="outline" className="w-full text-sm" size="sm">
                    Ver todas las materias
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Estadísticas</CardTitle>
                <CardDescription>
                  Tu rendimiento hasta ahora
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-muted-foreground">Respondidas</span>
                    </div>
                    <p className="text-2xl font-bold">{totalAnswers}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <ThumbsUp className="h-4 w-4 text-blue-500" />
                      <span className="text-sm text-muted-foreground">Correctas</span>
                    </div>
                    <p className="text-2xl font-bold">{totalCorrect}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-amber-500" />
                      <span className="text-sm text-muted-foreground">Horas</span>
                    </div>
                    <p className="text-2xl font-bold">{Math.round(totalHours)}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <LineChart className="h-4 w-4 text-purple-500" />
                      <span className="text-sm text-muted-foreground">Nivel</span>
                    </div>
                    <p className="text-2xl font-bold">{averageLevel}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <div className="col-span-1 md:col-span-1 lg:col-span-2">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Actividad reciente</CardTitle>
                <Button variant="ghost" size="sm" className="h-8 gap-1">
                  <span className="sr-only sm:not-sr-only text-xs">Ver todo</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all">
                <TabsList className="mb-4">
                  <TabsTrigger value="all" className="text-xs">Todas</TabsTrigger>
                  <TabsTrigger value="correct" className="text-xs">Correctas</TabsTrigger>
                  <TabsTrigger value="incorrect" className="text-xs">Incorrectas</TabsTrigger>
                </TabsList>
                
                <TabsContent value="all" className="m-0">
                  <div className="space-y-4">
                    {recentResponses.map((response) => (
                      <RecentActivityCard
                        key={response.id}
                        title={`${response.questions.subjects.name}`}
                        description={`Respondiste ${response.was_correct ? 'correctamente' : 'incorrectamente'} la pregunta ${response.question_id}`}
                        timestamp={new Date(response.created_at).toLocaleString()}
                        isCorrect={response.was_correct}
                      />
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="correct" className="m-0">
                  <div className="space-y-4">
                    {recentResponses
                      .filter(r => r.was_correct)
                      .map((response) => (
                        <RecentActivityCard
                          key={response.id}
                          title={`${response.questions.subjects.name}`}
                          description={`Respondiste correctamente la pregunta ${response.question_id}`}
                          timestamp={new Date(response.created_at).toLocaleString()}
                          isCorrect={true}
                        />
                      ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="incorrect" className="m-0">
                  <div className="space-y-4">
                    {recentResponses
                      .filter(r => !r.was_correct)
                      .map((response) => (
                        <RecentActivityCard
                          key={response.id}
                          title={`${response.questions.subjects.name}`}
                          description={`Respondiste incorrectamente la pregunta ${response.question_id}`}
                          timestamp={new Date(response.created_at).toLocaleString()}
                          isCorrect={false}
                        />
                      ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Logros</CardTitle>
          <CardDescription>
            Sigue sumando logros y mejora tus habilidades
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg">
              <div className="bg-amber-100 dark:bg-amber-900/50 p-3 rounded-full mb-3">
                <Award className="h-6 w-6 text-amber-500" />
              </div>
              <p className="font-medium text-center">Primer paso</p>
              <p className="text-xs text-muted-foreground text-center">Completa tu primera pregunta</p>
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <Clock className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">Disciplina</p>
              <p className="text-xs text-muted-foreground text-center">Estudia 3 días seguidos</p>
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <BookOpen className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">Explorador</p>
              <p className="text-xs text-muted-foreground text-center">Intenta 5 materias diferentes</p>
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <Zap className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">Genio</p>
              <p className="text-xs text-muted-foreground text-center">10 respuestas correctas seguidas</p>
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <LineChart className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">Avanzado</p>
              <p className="text-xs text-muted-foreground text-center">Alcanza nivel 8 en una materia</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}