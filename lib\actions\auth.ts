"use server";

import { createServerActionClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function signIn(email: string, password: string) {
  const supabase = createServerActionClient({ cookies });
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  if (error) throw error;
  return data;
}

export async function signUp(email: string, password: string, metadata: any) {
  const supabase = createServerActionClient({ cookies });
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: { data: metadata }
  });
  
  if (error) throw error;
  return data;
}

export async function signOut() {
  const supabase = createServerActionClient({ cookies });
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
}

export async function getSession() {
  try {
    const supabase = createServerActionClient({ cookies });
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.error('Session error:', error);
      return null;
    }
    return session;
  } catch (error) {
    console.error('Get session error:', error);
    return null;
  }
}

export async function updateUserAuth(email: string, metadata: any) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase.auth.updateUser({
    email,
    data: metadata
  });

  if (error) throw error;
  return data;
}

export async function updateUserPassword(newPassword: string) {
  const supabase = createServerActionClient({ cookies });
  const { data, error } = await supabase.auth.updateUser({
    password: newPassword
  });

  if (error) throw error;
  return data;
}