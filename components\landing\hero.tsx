"use client";

import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { ArrowRight, BookOpen, CheckCircle } from 'lucide-react';

export function LandingHero() {
  return (
    <section className="relative pt-28 pb-24 overflow-hidden">
      <div className="container px-4 mx-auto">
        <div className="flex flex-wrap items-center -mx-4">
          <motion.div 
            className="w-full lg:w-1/2 px-4 mb-16 lg:mb-0"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <span className="inline-block py-1 px-3 mb-4 text-xs font-medium text-blue-700 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
              Preparación preuniversitaria
            </span>
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              Prepárate para la{' '}
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                universidad
              </span>{' '}
              de tus sueños
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Con <strong>Manyas</strong>, la plataforma diseñada específicamente para estudiantes peruanos que buscan ingresar a las mejores universidades del país. Estudia con eficiencia, rinde al máximo.
            </p>
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-gray-700 dark:text-gray-200">Preguntas adaptadas a tu nivel</span>
              </div>
              <div className="flex items-center mb-4">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-gray-700 dark:text-gray-200">Soluciones explicadas paso a paso</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-gray-700 dark:text-gray-200">Contenido específico por universidad</span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <Link href="/register">
                <Button size="lg" className="group">
                  Empieza ahora
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Link href="#pricing">
                <Button variant="outline" size="lg">
                  Ver planes
                </Button>
              </Link>
            </div>
          </motion.div>
          <motion.div 
            className="w-full lg:w-1/2 px-4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="relative mx-auto md:mr-0 max-w-md md:max-w-none">
              <div className="relative overflow-hidden rounded-xl shadow-2xl">
                <Image
                  src="https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg"
                  alt="Estudiantes preparándose para la universidad"
                  width={600}
                  height={400}
                  priority
                  className="w-full h-[400px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6">
                  <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <BookOpen className="h-5 w-5 text-blue-600" />
                      <h3 className="font-semibold text-lg">Tu camino al éxito</h3>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Miles de estudiantes ya lograron ingresar con nuestro método. ¡Tú puedes ser el próximo!
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center transform -rotate-12">
                <span className="text-white text-center font-bold text-sm leading-tight">
                  Comienza<br />gratis
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}