import { CheckCircle, XCircle } from 'lucide-react';

interface QuestionFeedbackProps {
  isCorrect: boolean;
}

export function QuestionFeedback({ isCorrect }: QuestionFeedbackProps) {
  return (
    <div 
      className={`flex items-center gap-2 text-sm font-medium ${
        isCorrect 
          ? 'text-green-600 dark:text-green-500' 
          : 'text-red-600 dark:text-red-500'
      }`}
    >
      {isCorrect ? (
        <>
          <CheckCircle className="h-5 w-5" />
          ¡Correcto!
        </>
      ) : (
        <>
          <XCircle className="h-5 w-5" />
          Incorrecto
        </>
      )}
    </div>
  );
}