"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { motion } from 'framer-motion';
import { SubjectCard } from '@/components/dashboard/subject-card';
import { getInstitutions, getSubjectsWithDefaults } from '@/lib/queries/subjects';

interface Institution {
  id: string;
  name: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
  icon: string;
  description: string;
  color: string;
}

export default function SubjectsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch institutions and subjects
        const [institutionsData, subjectsData] = await Promise.all([
          getInstitutions(),
          getSubjectsWithDefaults()
        ]);

        setInstitutions(institutionsData);
        setSubjects(subjectsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    }
    fetchData();
  }, []);
  
  const getSubjectsByInstitution = (institutionId: string) => {
    return subjects.filter(subject => subject.institution_id === institutionId);
  };
  
  const filteredSubjects = searchTerm 
    ? subjects.filter(subject => 
        subject.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : subjects;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-1">
          Explorar materias
        </h2>
        <p className="text-muted-foreground">
          Elige una materia para comenzar a practicar preguntas
        </p>
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
          <Input
            type="search"
            placeholder="Buscar materias..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      {searchTerm ? (
        <div className="space-y-4">
          <h3 className="font-medium">Resultados de búsqueda</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredSubjects.map((subject) => (
              <SubjectCard key={subject.id} subject={subject} />
            ))}
          </div>
          {filteredSubjects.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                No se encontraron materias que coincidan con "{searchTerm}"
              </p>
            </div>
          )}
        </div>
      ) : (
        <Tabs defaultValue={institutions[0]?.id.toString()} className="space-y-4">
          <TabsList className="grid grid-cols-2 sm:grid-cols-4 mb-4">
            {institutions.map((institution) => (
              <TabsTrigger 
                key={institution.id}
                value={institution.id.toString()}
                className="text-xs sm:text-sm"
              >
                {institution.name}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {institutions.map((institution) => (
            <TabsContent 
              key={institution.id}
              value={institution.id.toString()}
              className="space-y-4"
            >
              <h3 className="font-medium text-lg">{institution.name}</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {getSubjectsByInstitution(institution.id).map((subject) => (
                  <SubjectCard key={subject.id} subject={subject} />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
}