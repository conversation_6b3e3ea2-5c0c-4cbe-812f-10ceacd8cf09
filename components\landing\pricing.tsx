"use client";

import Link from 'next/link';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle2 } from 'lucide-react';
import { SUBSCRIPTION_PLANS } from '@/lib/constants';

export function LandingPricing() {
  return (
    <section id="pricing" className="py-20">
      <div className="container px-4 mx-auto">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">
            Planes{" "}
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              simples y transparentes
            </span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Elige el plan que mejor se adapte a tus necesidades de preparación y 
            accede a todo nuestro contenido sin restricciones.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {SUBSCRIPTION_PLANS.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className={cn(
                "h-full",
                plan.popular && "border-2 border-blue-500 dark:border-blue-600 shadow-lg"
              )}>
                {plan.popular && (
                  <div className="absolute top-0 right-0 -mt-3 -mr-3">
                    <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded-full">
                      Recomendado
                    </span>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-6">
                    <p className="text-3xl font-bold">{plan.price}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{plan.duration}</p>
                    {plan.savings && (
                      <p className="text-sm font-medium text-green-600 mt-1">{plan.savings}</p>
                    )}
                  </div>
                  <ul className="space-y-3">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 mt-0.5 shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Link href="/register" className="w-full">
                    <Button 
                      className={cn(
                        "w-full", 
                        plan.popular 
                          ? "bg-blue-600 hover:bg-blue-700" 
                          : "bg-primary hover:bg-primary/90"
                      )}
                    >
                      Empezar ahora
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Todos los planes incluyen 7 días de prueba. Cancela cuando quieras, sin compromiso.
          </p>
        </div>
      </div>
    </section>
  );
}

function cn(...inputs: any[]) {
  return inputs.filter(Boolean).join(' ');
}